<?php

namespace App\Http\Controllers;

use App\Models\NaukriSahayta;
use App\Models\MultinationalCompany;
use App\Models\YadavVyaparGrahak;
use App\Models\DarshanikIshthal;
use App\Models\Membership;
use Illuminate\Http\Request;

class AnyaSevaController extends Controller
{
    /**
     * Show the naukri/rojgar/swarojgar sahayta form
     */
    public function showNaukriSahayataForm()
    {
        return view('anya-seva.naukri-sahayta');
    }

    /**
     * Submit the naukri/rojgar/swarojgar sahayta form
     */
    public function submitNaukriSahayta(Request $request)
    {
        $request->validate([
            'naam' => 'required|string|max:255',
            'pita_ka_naam' => 'required|string|max:255',
            'pata' => 'required|string|max:1000',
            'umra' => 'required|integer|min:18|max:65',
            'ahartayen' => 'required|string|max:1000',
            'ruchi' => 'required|string|max:1000',
            'mobile_number' => 'required|string|size:10|regex:/^[6-9][0-9]{9}$/',
            'biodata' => 'nullable|file|mimes:pdf,doc,docx|max:5120', // 5MB max
        ], [
            'naam.required' => 'नाम आवश्यक है।',
            'pita_ka_naam.required' => 'पिता का नाम आवश्यक है।',
            'pata.required' => 'पता आवश्यक है।',
            'umra.required' => 'उम्र आवश्यक है।',
            'umra.integer' => 'उम्र संख्या में होनी चाहिए।',
            'umra.min' => 'उम्र कम से कम 18 वर्ष होनी चाहिए।',
            'umra.max' => 'उम्र अधिकतम 65 वर्ष होनी चाहिए।',
            'ahartayen.required' => 'अहर्ताएं आवश्यक हैं।',
            'ruchi.required' => 'रुचि आवश्यक है।',
            'mobile_number.required' => 'मोबाइल नंबर आवश्यक है।',
            'mobile_number.size' => 'मोबाइल नंबर 10 अंकों का होना चाहिए।',
            'mobile_number.regex' => 'कृपया वैध मोबाइल नंबर दर्ज करें।',
            'biodata.mimes' => 'बायोडाटा केवल PDF, DOC या DOCX फॉर्मेट में होना चाहिए।',
            'biodata.max' => 'बायोडाटा फाइल 5MB से छोटी होनी चाहिए।',
        ]);

        $data = $request->except(['biodata']);

        // Handle biodata upload
        if ($request->hasFile('biodata')) {
            $data['biodata'] = $request->file('biodata')->store('naukri-sahayta/biodata', 'public');
        }

        NaukriSahayta::create($data);

        return redirect()->back()
            ->with('success', 'आपका आवेदन सफलतापूर्वक जमा हो गया है। हम जल्द ही आपसे संपर्क करेंगे।');
    }

    /**
     * Show the multinational company form
     */
    public function showMultinationalCompanyForm()
    {
        return view('anya-seva.multinational-company');
    }

    /**
     * Submit the multinational company form
     */
    public function submitMultinationalCompany(Request $request)
    {
        $request->validate([
            'naam' => 'required|string|max:255',
            'pita_ka_naam' => 'required|string|max:255',
            'pata' => 'required|string|max:1000',
            'umra' => 'required|integer|min:18|max:65',
            'ahartayen' => 'required|string|max:1000',
            'company_name' => 'required|string|max:255',
            'location' => 'required|string|max:255',
            'mobile_number' => 'required|string|size:10|regex:/^[6-9][0-9]{9}$/',
            'package' => 'required|string|max:255',
            'biodata' => 'nullable|file|mimes:pdf,doc,docx|max:5120', // 5MB max
            'media_link' => 'nullable|url|max:500',
        ], [
            'naam.required' => 'नाम आवश्यक है।',
            'pita_ka_naam.required' => 'पिता का नाम आवश्यक है।',
            'pata.required' => 'पता आवश्यक है।',
            'umra.required' => 'उम्र आवश्यक है।',
            'umra.integer' => 'उम्र संख्या में होनी चाहिए।',
            'umra.min' => 'उम्र कम से कम 18 वर्ष होनी चाहिए।',
            'umra.max' => 'उम्र अधिकतम 65 वर्ष होनी चाहिए।',
            'ahartayen.required' => 'अहर्ताएं आवश्यक हैं।',
            'company_name.required' => 'कंपनी का नाम आवश्यक है।',
            'location.required' => 'स्थान आवश्यक है।',
            'mobile_number.required' => 'मोबाइल नंबर आवश्यक है।',
            'mobile_number.size' => 'मोबाइल नंबर 10 अंकों का होना चाहिए।',
            'mobile_number.regex' => 'कृपया वैध मोबाइल नंबर दर्ज करें।',
            'package.required' => 'पैकेज आवश्यक है।',
            'biodata.mimes' => 'बायोडाटा केवल PDF, DOC या DOCX फॉर्मेट में होना चाहिए।',
            'biodata.max' => 'बायोडाटा फाइल 5MB से छोटी होनी चाहिए।',
            'media_link.url' => 'कृपया वैध URL दर्ज करें।',
            'media_link.max' => 'मीडिया लिंक 500 अक्षरों से कम होना चाहिए।',
        ]);

        $data = $request->except(['biodata']);

        // Handle biodata upload
        if ($request->hasFile('biodata')) {
            $data['biodata'] = $request->file('biodata')->store('multinational-company/biodata', 'public');
        }

        MultinationalCompany::create($data);

        return redirect()->back()
            ->with('success', 'आपका आवेदन सफलतापूर्वक जमा हो गया है। हम जल्द ही आपसे संपर्क करेंगे।');
    }

    /**
     * Show the yadav vyapar evam grahak form
     */
    public function showYadavVyaparGrahakForm()
    {
        $categories = YadavVyaparGrahak::getCategories();
        return view('anya-seva.yadav-vyapar-grahak', compact('categories'));
    }

    /**
     * Submit the yadav vyapar evam grahak form
     */
    public function submitYadavVyaparGrahak(Request $request)
    {
        $request->validate([
            'category' => 'required|in:swasthya,gharelu,salahkar,anya vyavasay',
            'sadasyata_kramank' => 'required|string|max:255',
            'sadasyata_prakar' => 'required|string|max:255',
            'naam' => 'required|string|max:255',
            'ajivika' => 'required|string|max:255',
            'ward' => 'required|string|max:255',
            'vikaskhand' => 'required|string|max:255',
            'jila' => 'required|string|max:255',
            'mobile_number' => 'required|string|size:10|regex:/^[6-9][0-9]{9}$/',
            'address' => 'required|string|max:1000',
            'photo' => 'nullable|image|mimes:jpeg,jpg,png|max:2048', // 2MB max
            'gmap_link' => 'nullable|string|max:500',
        ], [
            'category.required' => 'श्रेणी चुनना आवश्यक है।',
            'category.in' => 'कृपया वैध श्रेणी चुनें।',
            'sadasyata_kramank.required' => 'सदस्यता क्रमांक आवश्यक है।',
            'sadasyata_prakar.required' => 'सदस्यता प्रकार आवश्यक है।',
            'naam.required' => 'नाम आवश्यक है।',
            'ajivika.required' => 'आजीविका आवश्यक है।',
            'ward.required' => 'वार्ड आवश्यक है।',
            'vikaskhand.required' => 'विकासखंड आवश्यक है।',
            'jila.required' => 'जिला आवश्यक है।',
            'mobile_number.required' => 'मोबाइल नंबर आवश्यक है।',
            'mobile_number.size' => 'मोबाइल नंबर 10 अंकों का होना चाहिए।',
            'mobile_number.regex' => 'कृपया वैध मोबाइल नंबर दर्ज करें।',
            'address.required' => 'पता आवश्यक है।',
            'photo.image' => 'केवल इमेज फाइल अपलोड करें।',
            'photo.mimes' => 'फोटो JPG, JPEG या PNG फॉर्मेट में होनी चाहिए।',
            'photo.max' => 'फोटो 2MB से छोटी होनी चाहिए।',
            'gmap_link.max' => 'गूगल मैप लिंक 500 अक्षरों से कम होना चाहिए।',
        ]);

        $data = $request->except(['photo']);

        // Handle photo upload
        if ($request->hasFile('photo')) {
            $data['photo'] = $request->file('photo')->store('yadav-vyapar-grahak/photos', 'public');
        }

        YadavVyaparGrahak::create($data);

        return redirect()->back()
            ->with('success', 'आपकी जानकारी सफलतापूर्वक जमा हो गई है। धन्यवाद!');
    }

    /**
     * Show the Darshanik Ishthal form
     */
    public function showDarshanikIshthalForm()
    {
        return view('anya-seva.darshanik-ishthal');
    }

    /**
     * Submit the Darshanik Ishthal form
     */
    public function submitDarshanikIshthal(Request $request)
    {
        $request->validate([
            'place_name' => 'required|string|max:255',
            'guide_name' => 'required|string|max:255',
            'guide_mobile' => 'required|string|size:10|regex:/^[6-9][0-9]{9}$/',
            'guide_address' => 'nullable|string|max:1000',
            'place_details' => 'nullable|string|max:2000',
            'guide_details' => 'nullable|string|max:1000',
            'transport_info' => 'nullable|string|max:1000',
            'accommodation_info' => 'nullable|string|max:1000',
            'other_info' => 'nullable|string|max:1000',
            'submitted_by_name' => 'required|string|max:255',
            'submitted_by_mobile' => 'required|string|size:10|regex:/^[6-9][0-9]{9}$/',
            'membership_number' => 'nullable|string|max:50',
        ], [
            'place_name.required' => 'दर्शनिक स्थल का नाम आवश्यक है।',
            'guide_name.required' => 'गाइड का नाम आवश्यक है।',
            'guide_mobile.required' => 'गाइड का मोबाइल नंबर आवश्यक है।',
            'guide_mobile.size' => 'गाइड का मोबाइल नंबर 10 अंकों का होना चाहिए।',
            'guide_mobile.regex' => 'कृपया वैध मोबाइल नंबर दर्ज करें।',
            'submitted_by_name.required' => 'आपका नाम आवश्यक है।',
            'submitted_by_mobile.required' => 'आपका मोबाइल नंबर आवश्यक है।',
            'submitted_by_mobile.size' => 'मोबाइल नंबर 10 अंकों का होना चाहिए।',
            'submitted_by_mobile.regex' => 'कृपया वैध मोबाइल नंबर दर्ज करें।',
        ]);

        DarshanikIshthal::create($request->all());

        return redirect()->back()
            ->with('success', 'दर्शनिक स्थल की जानकारी सफलतापूर्वक जमा हो गई है। सत्यापन के बाद यह सार्वजनिक सूची में दिखाई देगी। धन्यवाद!');
    }

    /**
     * Show Darshanik Ishthal reports
     */
    public function showDarshanikIshthalReports()
    {
        $reports = DarshanikIshthal::public()
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        return view('anya-seva.darshanik-ishthal-reports', compact('reports'));
    }

    /**
     * Verify membership and show full details
     */
    public function verifyDarshanikIshthalDetails(Request $request, $id)
    {
        $request->validate([
            'membership_number' => 'required|string',
        ], [
            'membership_number.required' => 'सदस्यता संख्या आवश्यक है।',
        ]);

        $entry = DarshanikIshthal::verifyWithMembership($id, $request->membership_number);

        if (!$entry) {
            return redirect()->back()
                ->with('error', 'अवैध सदस्यता संख्या या रिकॉर्ड नहीं मिला।');
        }

        return view('anya-seva.darshanik-ishthal-details', compact('entry'));
    }

    /**
     * Show Naukri Sahayta reports
     */
    public function showNaukriSahayataReports()
    {
        $reports = NaukriSahayta::orderBy('created_at', 'desc')->paginate(12);
        return view('anya-seva.naukri-sahayta-reports', compact('reports'));
    }

    /**
     * Verify membership and show Naukri Sahayta details
     */
    public function verifyNaukriSahayataDetails(Request $request, $id)
    {
        $request->validate([
            'membership_number' => 'required|string',
        ], [
            'membership_number.required' => 'सदस्यता संख्या आवश्यक है।',
        ]);

        // Check if membership number exists in database
        $membership = Membership::where('membership_number', $request->membership_number)
            ->where('status', 'approved')
            ->first();

        if (!$membership) {
            return redirect()->back()
                ->with('error', 'अवैध सदस्यता संख्या।');
        }

        $entry = NaukriSahayta::findOrFail($id);
        return view('anya-seva.naukri-sahayta-details', compact('entry'));
    }

    /**
     * Show Multinational Company reports
     */
    public function showMultinationalCompanyReports()
    {
        $reports = MultinationalCompany::orderBy('created_at', 'desc')->paginate(12);
        return view('anya-seva.multinational-company-reports', compact('reports'));
    }

    /**
     * Verify membership and show Multinational Company details
     */
    public function verifyMultinationalCompanyDetails(Request $request, $id)
    {
        $request->validate([
            'membership_number' => 'required|string',
        ], [
            'membership_number.required' => 'सदस्यता संख्या आवश्यक है।',
        ]);

        // Check if membership number exists in database
        $membership = Membership::where('membership_number', $request->membership_number)
            ->where('status', 'approved')
            ->first();

        if (!$membership) {
            return redirect()->back()
                ->with('error', 'अवैध सदस्यता संख्या।');
        }

        $entry = MultinationalCompany::findOrFail($id);
        return view('anya-seva.multinational-company-details', compact('entry'));
    }

    /**
     * Show Yadav Vyapar Grahak reports
     */
    public function showYadavVyaparGrahakReports()
    {
        $reports = YadavVyaparGrahak::orderBy('created_at', 'desc')->paginate(12);
        return view('anya-seva.yadav-vyapar-grahak-reports', compact('reports'));
    }

    /**
     * Verify membership and show Yadav Vyapar Grahak details
     */
    public function verifyYadavVyaparGrahakDetails(Request $request, $id)
    {
        $request->validate([
            'membership_number' => 'required|string',
        ], [
            'membership_number.required' => 'सदस्यता संख्या आवश्यक है।',
        ]);

        // Check if membership number exists in database
        $membership = Membership::where('membership_number', $request->membership_number)
            ->where('status', 'approved')
            ->first();

        if (!$membership) {
            return redirect()->back()
                ->with('error', 'अवैध सदस्यता संख्या।');
        }

        $entry = YadavVyaparGrahak::findOrFail($id);
        return view('anya-seva.yadav-vyapar-grahak-details', compact('entry'));
    }
}
