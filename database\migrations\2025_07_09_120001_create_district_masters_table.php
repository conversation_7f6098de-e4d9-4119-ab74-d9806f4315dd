<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('district_masters', function (Blueprint $table) {
            $table->id();
            $table->string('district_lgd_code')->unique(); // Unique LGD code for district
            $table->string('district_name_eng'); // District name in English
            $table->string('district_name_hin')->nullable(); // District name in Hindi
            $table->string('district_short_name')->nullable(); // Short name for district
            $table->string('division_code'); // Foreign key to division_masters
            $table->boolean('is_active')->default(true); // Active/Inactive status
            $table->integer('display_order')->default(0); // For ordering in dropdowns
            $table->timestamps();

            // Add foreign key constraint
            $table->foreign('division_code')->references('division_code')->on('division_masters')->onDelete('cascade');

            // Add indexes for better performance
            $table->index('district_lgd_code');
            $table->index('division_code');
            $table->index('is_active');
            $table->index('display_order');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('district_masters');
    }
};
