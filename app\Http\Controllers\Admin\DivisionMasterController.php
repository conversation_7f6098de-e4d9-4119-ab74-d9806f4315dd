<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\DivisionMaster;
use Illuminate\Http\Request;

class DivisionMasterController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = DivisionMaster::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('division_name_eng', 'like', "%{$search}%")
                  ->orWhere('division_name_hin', 'like', "%{$search}%")
                  ->orWhere('division_code', 'like', "%{$search}%");
            });
        }

        // Status filter
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->active();
            } elseif ($request->status === 'inactive') {
                $query->inactive();
            }
        }

        $divisions = $query->ordered()->paginate(15);

        return view('admin.division-masters.index', compact('divisions'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.division-masters.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'division_code' => 'required|string|max:255|unique:division_masters,division_code',
                'division_name_eng' => 'required|string|max:255',
                'division_name_hin' => 'required|string|max:255',
                'is_active' => 'boolean',
                'display_order' => 'nullable|integer|min:0',
            ]);

            $validated['is_active'] = $request->has('is_active');
            $validated['display_order'] = $validated['display_order'] ?? 0;

            DivisionMaster::create($validated);

            return redirect()->route('admin.division-masters.index')
                           ->with('success', 'संभाग सफलतापूर्वक जोड़ा गया।');
        } catch (\Exception $e) {
            return redirect()->back()
                           ->withInput()
                           ->with('error', 'संभाग जोड़ने में त्रुटि: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(DivisionMaster $divisionMaster)
    {
        return view('admin.division-masters.show', compact('divisionMaster'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(DivisionMaster $divisionMaster)
    {
        return view('admin.division-masters.edit', compact('divisionMaster'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, DivisionMaster $divisionMaster)
    {
        try {
            $validated = $request->validate([
                'division_code' => 'required|string|max:255|unique:division_masters,division_code,' . $divisionMaster->id,
                'division_name_eng' => 'required|string|max:255',
                'division_name_hin' => 'required|string|max:255',
                'is_active' => 'boolean',
                'display_order' => 'nullable|integer|min:0',
            ]);

            $validated['is_active'] = $request->has('is_active');
            $validated['display_order'] = $validated['display_order'] ?? 0;

            $divisionMaster->update($validated);

            return redirect()->route('admin.division-masters.index')
                           ->with('success', 'संभाग सफलतापूर्वक अपडेट किया गया।');
        } catch (\Exception $e) {
            return redirect()->back()
                           ->withInput()
                           ->with('error', 'संभाग अपडेट करने में त्रुटि: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(DivisionMaster $divisionMaster)
    {
        try {
            $divisionMaster->delete();
            return redirect()->route('admin.division-masters.index')
                           ->with('success', 'संभाग सफलतापूर्वक हटाया गया।');
        } catch (\Exception $e) {
            return redirect()->back()
                           ->with('error', 'संभाग हटाने में त्रुटि: ' . $e->getMessage());
        }
    }
}
