<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\MultinationalCompany;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class MultinationalCompanyController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('admin.auth');
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = MultinationalCompany::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('naam', 'like', "%{$search}%")
                  ->orWhere('pita_ka_naam', 'like', "%{$search}%")
                  ->orWhere('company_name', 'like', "%{$search}%")
                  ->orWhere('location', 'like', "%{$search}%")
                  ->orWhere('mobile_number', 'like', "%{$search}%")
                  ->orWhere('package', 'like', "%{$search}%");
            });
        }

        $multinationalCompanies = $query->orderBy('created_at', 'desc')->paginate(15);

        return view('admin.multinational-companies.index', compact('multinationalCompanies'));
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $company = MultinationalCompany::findOrFail($id);
        return view('admin.multinational-companies.show', compact('company'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $company = MultinationalCompany::findOrFail($id);
        return view('admin.multinational-companies.edit', compact('company'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            $company = MultinationalCompany::findOrFail($id);

            $validated = $request->validate([
                'naam' => 'required|string|max:255',
                'pita_ka_naam' => 'required|string|max:255',
                'pata' => 'required|string|max:1000',
                'umra' => 'required|integer|min:18|max:65',
                'ahartayen' => 'required|string|max:1000',
                'company_name' => 'required|string|max:255',
                'location' => 'required|string|max:255',
                'mobile_number' => 'required|string|size:10|regex:/^[6-9][0-9]{9}$/',
                'package' => 'required|string|max:255',
                'biodata' => 'nullable|file|mimes:pdf,doc,docx|max:5120', // 5MB max
                'media_link' => 'nullable|url|max:500',
            ]);

            $data = $validated;

            // Handle biodata upload if new file is provided
            if ($request->hasFile('biodata') && $request->file('biodata')->isValid()) {
                // Delete old file
                $company->deleteBiodataFile();

                // Store new file
                $data['biodata'] = $request->file('biodata')->store('multinational-company/biodata', 'public');
            }

            $company->update($data);

            return redirect()
                ->route('admin.multinational-companies.index')
                ->with('success', 'मल्टीनेशनल कंपनी रिकॉर्ड सफलतापूर्वक अपडेट हो गया।');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->withErrors(['error' => 'रिकॉर्ड अपडेट करने में त्रुटि: ' . $e->getMessage()]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $company = MultinationalCompany::findOrFail($id);
            $company->delete(); // This will also delete the biodata file due to model boot method

            return redirect()
                ->route('admin.multinational-companies.index')
                ->with('success', 'मल्टीनेशनल कंपनी रिकॉर्ड सफलतापूर्वक डिलीट हो गया।');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['error' => 'रिकॉर्ड डिलीट करने में त्रुटि: ' . $e->getMessage()]);
        }
    }

    /**
     * Download biodata file
     */
    public function downloadBiodata(string $id)
    {
        try {
            $company = MultinationalCompany::findOrFail($id);
            
            if (!$company->biodata || !Storage::exists($company->biodata)) {
                return redirect()->back()
                    ->withErrors(['error' => 'बायोडाटा फाइल उपलब्ध नहीं है।']);
            }

            return Storage::download($company->biodata, $company->naam . '_biodata.' . pathinfo($company->biodata, PATHINFO_EXTENSION));

        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['error' => 'फाइल डाउनलोड करने में त्रुटि: ' . $e->getMessage()]);
        }
    }
}
