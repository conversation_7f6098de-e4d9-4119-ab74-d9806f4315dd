<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\MembershipVargMaster;
use App\Models\EducationQualification;
use App\Models\OfficeMaster;
use App\Models\DepartmentMaster;

class MasterDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Call individual seeders for new master data
        $this->call([
            DivisionMasterSeeder::class,
            DistrictMasterSeeder::class,
            VikaskhandMasterSeeder::class,
        ]);

        // Membership Varg Masters
        $membershipVargs = [
            ['name' => 'सामान्य सदस्य', 'name_english' => 'General Member', 'description' => 'सामान्य सदस्यता', 'fee' => 100.00, 'display_order' => 1],
            ['name' => 'आजीवन सदस्य', 'name_english' => 'Lifetime Member', 'description' => 'आजीवन सदस्यता', 'fee' => 1000.00, 'display_order' => 2],
            ['name' => 'संरक्षक सदस्य', 'name_english' => 'Patron Member', 'description' => 'संरक्षक सदस्यता', 'fee' => 5000.00, 'display_order' => 3],
            ['name' => 'युवा सदस्य', 'name_english' => 'Youth Member', 'description' => 'युवा सदस्यता (18-35 वर्ष)', 'fee' => 50.00, 'display_order' => 4],
        ];

        foreach ($membershipVargs as $varg) {
            MembershipVargMaster::create($varg);
        }

        // Education Qualifications
        $educationQualifications = [
            ['name' => 'प्राथमिक शिक्षा', 'name_english' => 'Primary Education', 'level' => 'primary', 'display_order' => 1],
            ['name' => 'माध्यमिक शिक्षा', 'name_english' => 'Secondary Education', 'level' => 'secondary', 'display_order' => 2],
            ['name' => 'उच्च माध्यमिक', 'name_english' => 'Higher Secondary', 'level' => 'higher_secondary', 'display_order' => 3],
            ['name' => 'स्नातक', 'name_english' => 'Bachelor\'s Degree', 'level' => 'undergraduate', 'display_order' => 4],
            ['name' => 'स्नातकोत्तर', 'name_english' => 'Master\'s Degree', 'level' => 'postgraduate', 'display_order' => 5],
            ['name' => 'डॉक्टरेट', 'name_english' => 'Doctorate', 'level' => 'doctorate', 'display_order' => 6],
            ['name' => 'डिप्लोमा', 'name_english' => 'Diploma', 'level' => 'diploma', 'display_order' => 7],
            ['name' => 'व्यावसायिक प्रशिक्षण', 'name_english' => 'Vocational Training', 'level' => 'vocational', 'display_order' => 8],
        ];

        foreach ($educationQualifications as $qualification) {
            EducationQualification::create($qualification);
        }

        // Office Masters
        $officeMasters = [
            ['name' => 'सरकारी कार्यालय', 'name_english' => 'Government Office', 'category' => 'government', 'display_order' => 1],
            ['name' => 'निजी कंपनी', 'name_english' => 'Private Company', 'category' => 'private', 'display_order' => 2],
            ['name' => 'स्वरोजगार', 'name_english' => 'Self Employed', 'category' => 'self_employed', 'display_order' => 3],
            ['name' => 'शिक्षण संस्थान', 'name_english' => 'Educational Institution', 'category' => 'education', 'display_order' => 4],
            ['name' => 'स्वास्थ्य सेवा', 'name_english' => 'Healthcare', 'category' => 'healthcare', 'display_order' => 5],
            ['name' => 'कृषि', 'name_english' => 'Agriculture', 'category' => 'agriculture', 'display_order' => 6],
            ['name' => 'व्यापार', 'name_english' => 'Business', 'category' => 'business', 'display_order' => 7],
            ['name' => 'सेवानिवृत्त', 'name_english' => 'Retired', 'category' => 'retired', 'display_order' => 8],
            ['name' => 'छात्र', 'name_english' => 'Student', 'category' => 'student', 'display_order' => 9],
        ];

        foreach ($officeMasters as $office) {
            OfficeMaster::create($office);
        }

        // Department Masters
        $departmentMasters = [
            ['name' => 'शिक्षा विभाग', 'name_english' => 'Education Department', 'category' => 'government', 'ministry' => 'शिक्षा मंत्रालय', 'display_order' => 1],
            ['name' => 'स्वास्थ्य विभाग', 'name_english' => 'Health Department', 'category' => 'government', 'ministry' => 'स्वास्थ्य मंत्रालय', 'display_order' => 2],
            ['name' => 'पुलिस विभाग', 'name_english' => 'Police Department', 'category' => 'government', 'ministry' => 'गृह मंत्रालय', 'display_order' => 3],
            ['name' => 'राजस्व विभाग', 'name_english' => 'Revenue Department', 'category' => 'government', 'ministry' => 'वित्त मंत्रालय', 'display_order' => 4],
            ['name' => 'कृषि विभाग', 'name_english' => 'Agriculture Department', 'category' => 'government', 'ministry' => 'कृषि मंत्रालय', 'display_order' => 5],
            ['name' => 'वन विभाग', 'name_english' => 'Forest Department', 'category' => 'government', 'ministry' => 'पर्यावरण मंत्रालय', 'display_order' => 6],
            ['name' => 'परिवहन विभाग', 'name_english' => 'Transport Department', 'category' => 'government', 'ministry' => 'परिवहन मंत्रालय', 'display_order' => 7],
            ['name' => 'लोक निर्माण विभाग', 'name_english' => 'Public Works Department', 'category' => 'government', 'ministry' => 'शहरी विकास मंत्रालय', 'display_order' => 8],
            ['name' => 'न्यायपालिका', 'name_english' => 'Judiciary', 'category' => 'autonomous', 'ministry' => 'न्याय मंत्रालय', 'display_order' => 9],
            ['name' => 'रक्षा विभाग', 'name_english' => 'Defence Department', 'category' => 'government', 'ministry' => 'रक्षा मंत्रालय', 'display_order' => 10],
            ['name' => 'सूचना प्रौद्योगिकी विभाग', 'name_english' => 'IT Department', 'category' => 'government', 'ministry' => 'इलेक्ट्रॉनिक्स मंत्रालय', 'display_order' => 11],
            ['name' => 'निजी क्षेत्र', 'name_english' => 'Private Sector', 'category' => 'private', 'ministry' => null, 'display_order' => 12],
        ];

        foreach ($departmentMasters as $department) {
            DepartmentMaster::create($department);
        }
    }
}
