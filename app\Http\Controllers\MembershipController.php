<?php

namespace App\Http\Controllers;

use App\Models\Membership;
use App\Models\VaivahikPanjiyan;
use App\Models\MembershipVargMaster;
use App\Models\EducationQualification;
use App\Models\OfficeMaster;
use App\Models\DepartmentMaster;
use App\Models\YadavVarg;
use App\Models\DivisionMaster;
use App\Models\DistrictMaster;
use App\Models\VikaskhandMaster;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;

class MembershipController extends Controller
{
    /**
     * Show the membership application form with master data.
     */
    public function showApplicationForm()
    {
        $membershipVargs = MembershipVargMaster::active()->ordered()->get();
        $educationQualifications = EducationQualification::active()->ordered()->get();
        $officeMasters = OfficeMaster::active()->ordered()->get();
        $departmentMasters = DepartmentMaster::active()->ordered()->get();
        $yadavVargs = YadavVarg::active()->ordered()->get();
        $divisionMasters = DivisionMaster::active()->ordered()->get();
        $districtMasters = DistrictMaster::active()->ordered()->get();
        $vikaskhandMasters = VikaskhandMaster::active()->ordered()->get();

        return view('sadasya.aavedan', compact(
            'membershipVargs', 'educationQualifications', 'officeMasters',
            'departmentMasters', 'yadavVargs', 'divisionMasters',
            'districtMasters', 'vikaskhandMasters'
        ));
    }

    public function vaivahikPanjiyan()
    {
        // Fetch all vaivahik panjiyan data with pagination
        $vaivahikData = VaivahikPanjiyan::latest()->paginate(12);

        // Check if user is logged in with membership guard
        $isLoggedIn = auth('member')->check();

        return view('sadasya.vaivahik-panjiyan', compact('vaivahikData', 'isLoggedIn'));
    }
    public function vaivahikPanjiyanSubmit(Request $request)
    {
        $request->validate([
            'naam' => 'required|string|max:255',
            'ling' => 'required|string',
            'mobile' => 'required|string|size:10|unique:vaivahik_panjiyans,mobile',
            'owner_email' => 'required|email|unique:vaivahik_panjiyans,owner_email',
            'owner_password' => 'required|string|min:8',
            'janmatithi' => 'nullable|date',
            'janmasamay_din' => 'nullable|string',
            'pita_ka_naam' => 'nullable|string',
            'mata_ka_naam' => 'nullable|string',
            'bhai_bahan_vivran' => 'nullable|string',
            'pata' => 'nullable|string',
            'jati' => 'nullable|string',
            'upjati' => 'nullable|string',
            'gotra' => 'nullable|string',
            'uchai' => 'nullable|integer|min:100|max:272',
            'saikshanik_yogyata' => 'nullable|string',
            'education_qualification_id' => 'nullable|exists:education_qualifications,id',
            'vartaman_karya' => 'nullable|string',
            'upjivika' => 'nullable|string|max:255',
            'family_member_membership_number' => 'required|string|max:255',
            'office_master_id' => 'nullable|exists:office_masters,id',
            'department_master_id' => 'nullable|exists:department_masters,id',
            'yadav_varg_id' => 'nullable|exists:yadav_vargs,id',
            'biodata' => 'nullable|file|mimes:pdf|max:5120', // 5MB max
            'photos' => 'nullable|array|max:4',
            'photos.*' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
        ], [
            'naam.required' => 'नाम आवश्यक है।',
            'ling.required' => 'लिंग चुनना आवश्यक है।',
            'mobile.required' => 'मोबाइल नंबर आवश्यक है।',
            'mobile.size' => 'मोबाइल नंबर 10 अंकों का होना चाहिए।',
            'mobile.unique' => 'यह मोबाइल नंबर पहले से पंजीकृत है।',
            'owner_email.required' => 'ईमेल पता आवश्यक है।',
            'owner_email.email' => 'कृपया वैध ईमेल पता दर्ज करें।',
            'owner_email.unique' => 'यह ईमेल पता पहले से पंजीकृत है।',
            'owner_password.required' => 'पासवर्ड आवश्यक है।',
            'owner_password.min' => 'पासवर्ड कम से कम 8 अक्षर का होना चाहिए।',
            'uchai.integer' => 'ऊंचाई संख्या में होनी चाहिए।',
            'uchai.min' => 'ऊंचाई कम से कम 100 से.मी. होनी चाहिए।',
            'uchai.max' => 'ऊंचाई अधिकतम 250 से.मी. होनी चाहिए।',
            'biodata.mimes' => 'बायोडाटा केवल PDF फॉर्मेट में होना चाहिए।',
            'biodata.max' => 'बायोडाटा फाइल 5MB से छोटी होनी चाहिए।',
            'photos.max' => 'अधिकतम 4 फोटो अपलोड कर सकते हैं।',
            'photos.*.image' => 'केवल इमेज फाइल अपलोड करें।',
            'photos.*.mimes' => 'फोटो JPG, PNG या JPEG फॉर्मेट में होनी चाहिए।',
            'photos.*.max' => 'प्रत्येक फोटो 2MB से छोटी होनी चाहिए।',
            'upjivika.max' => 'उपजीविका 255 अक्षरों से कम होनी चाहिए।',
            'family_member_membership_number.required' => 'परिवारिक सदस्य की सदस्यता संख्या आवश्यक है।',
            'family_member_membership_number.max' => 'सदस्यता संख्या 255 अक्षरों से कम होनी चाहिए।',
        ]);

        // Verify family member membership number (now required)
        $membershipExists = Membership::where('membership_number', $request->family_member_membership_number)
            ->where('status', Membership::STATUS_APPROVED)
            ->where('is_active', true)
            ->exists();

        if (!$membershipExists) {
            return back()->withErrors([
                'family_member_membership_number' => 'यह सदस्यता संख्या मान्य नहीं है या स्वीकृत नहीं है। कृपया सही सदस्यता संख्या दर्ज करें।'
            ])->withInput();
        }

        $data = $request->except(['photos', 'biodata', 'owner_password']);

        // Hash password and activate account (now required)
        $data['owner_password'] = Hash::make($request->owner_password);
        $data['is_active'] = true;
        $data['status'] = VaivahikPanjiyan::STATUS_PENDING; // Pending admin approval

        // Handle biodata upload
        if ($request->hasFile('biodata')) {
            $data['biodata'] = $request->file('biodata')->store('biodata', 'public');
        }

        // Handle exactly 4 photos
        if ($request->hasFile('photos')) {
            for ($i = 0; $i < 4; $i++) {
                $photoField = 'photo' . ($i + 1);
                if (isset($request->file('photos')[$i])) {
                    $data[$photoField] = $request->file('photos')[$i]->store('photos', 'public');
                }
            }
        }

        $vaivahikPanjiyan = VaivahikPanjiyan::create($data);

        // Auto-login the user after successful registration
        Auth::guard('vaivahik')->login($vaivahikPanjiyan);

        return redirect()->route('vaivahik.dashboard')
            ->with('success', 'वैवाहिक पंजीयन सफल! आपका खाता बन गया है। प्रोफाइल स्वीकृति की प्रतीक्षा करें।')
            ->with('info', 'आप अब अपने डैशबोर्ड में हैं। प्रोफाइल स्वीकृति के बाद सभी सुविधाएं उपलब्ध होंगी।');
    }

    public function vaivahikPanjiyanNew()
    {
        $educationQualifications = EducationQualification::active()->ordered()->get();
        $officeMasters = OfficeMaster::active()->ordered()->get();
        $departmentMasters = DepartmentMaster::active()->ordered()->get();
        $yadavVargs = YadavVarg::active()->ordered()->get();

        return view('sadasya.vaivahik-panjiyan-form', compact('educationQualifications', 'officeMasters', 'departmentMasters', 'yadavVargs'));
    }



    // For Sadasyata Aavedan


public function submitSadasyata(Request $request)
{
    // Validate the incoming request data
    $validatedData = $request->validate([
        'photo' => 'nullable|image|mimes:jpg,jpeg,png|max:2048',
        'signature' => 'nullable|image|mimes:jpg,jpeg,png|max:1024',
        'name' => 'required|string|max:255',
        'fathers_husband_name' => 'nullable|string|max:255',
        'mobile_number' => 'required|digits:10|starts_with:6,7,8,9|unique:memberships,mobile_number',
        'email' => 'required|email|unique:memberships,email',
        'password' => 'required|string|min:8|confirmed',
        'birth_date' => 'required|date',
        'marital_status' => 'nullable|string',
        'vivah_tithi' => 'nullable|date',
        'family_members' => 'nullable|integer|min:1',
        'education' => 'nullable|string',
        'education_qualification_id' => 'nullable|exists:education_qualifications,id',
        'course_stream_name' => 'nullable|string|max:255',
        'caste_details' => 'nullable|string|max:255',
        'vibhagiy_padnaam' => 'nullable|string|max:255',
        'department_name' => 'nullable|string|max:255',
        'department_master_id' => 'nullable|exists:department_masters,id',
        'office' => 'nullable|string|max:255',
        'office_master_id' => 'nullable|exists:office_masters,id',
        'karyalay_ka_pata' => 'nullable|string',
        'vartaman_pata' => 'nullable|string',
        'isthayi_pata' => 'nullable|string',
        'address' => 'nullable|string',
        'yadav_varg_id' => 'nullable|exists:yadav_vargs,id',
        'division_master_id' => 'required|exists:division_masters,id',
        'district_master_id' => 'required|exists:district_masters,id',
        'vikaskhand_master_id' => 'required|exists:vikaskhand_masters,id',
        'child_name.*' => 'nullable|string|max:255',
        'child_gender.*' => 'nullable|string',
        'child_dob.*' => 'nullable|date',
        'child_education.*' => 'nullable|string|max:255',
        'child_occupation.*' => 'nullable|string|max:255',
        'child_marital_status.*' => 'nullable|string',
        'child_spouse_name.*' => 'nullable|string|max:255',
        'child_spouse_occupation.*' => 'nullable|string|max:255',
        'membership_type' => 'required|string',
        'membership_varg_id' => 'nullable|exists:membership_varg_masters,id',
        'member_name_signature' => 'required|string|max:255',
        'vibhag' => 'nullable|string',
        'mobile' => 'required|digits:10|starts_with:6,7,8,9',
        'proposer_address' => 'nullable|string',
        'declaration' => 'required|accepted',
        // Ancestor dynamic fields for all generation levels (1-7)
        'generation_level.*' => 'nullable|string',
        'ancestor_name_1.*' => 'nullable|string|max:255',
        'ancestor_birth_date_1.*' => 'nullable|string|max:255',
        'ancestor_death_date_1.*' => 'nullable|string|max:255',
        'ancestor_birth_place_1.*' => 'nullable|string|max:255',
        'ancestor_occupation_1.*' => 'nullable|string|max:255',
        'ancestor_spouse_1.*' => 'nullable|string|max:255',
        'ancestor_children_count_1.*' => 'nullable|integer|min:0',
        'ancestor_gotra_1.*' => 'nullable|string|max:255',
        'ancestor_name_2.*' => 'nullable|string|max:255',
        'ancestor_birth_date_2.*' => 'nullable|string|max:255',
        'ancestor_death_date_2.*' => 'nullable|string|max:255',
        'ancestor_birth_place_2.*' => 'nullable|string|max:255',
        'ancestor_occupation_2.*' => 'nullable|string|max:255',
        'ancestor_spouse_2.*' => 'nullable|string|max:255',
        'ancestor_children_count_2.*' => 'nullable|integer|min:0',
        'ancestor_gotra_2.*' => 'nullable|string|max:255',
        'ancestor_name_3.*' => 'nullable|string|max:255',
        'ancestor_birth_date_3.*' => 'nullable|string|max:255',
        'ancestor_death_date_3.*' => 'nullable|string|max:255',
        'ancestor_birth_place_3.*' => 'nullable|string|max:255',
        'ancestor_occupation_3.*' => 'nullable|string|max:255',
        'ancestor_spouse_3.*' => 'nullable|string|max:255',
        'ancestor_children_count_3.*' => 'nullable|integer|min:0',
        'ancestor_gotra_3.*' => 'nullable|string|max:255',
        'ancestor_name_4.*' => 'nullable|string|max:255',
        'ancestor_birth_date_4.*' => 'nullable|string|max:255',
        'ancestor_death_date_4.*' => 'nullable|string|max:255',
        'ancestor_birth_place_4.*' => 'nullable|string|max:255',
        'ancestor_occupation_4.*' => 'nullable|string|max:255',
        'ancestor_spouse_4.*' => 'nullable|string|max:255',
        'ancestor_children_count_4.*' => 'nullable|integer|min:0',
        'ancestor_gotra_4.*' => 'nullable|string|max:255',
        'ancestor_name_5.*' => 'nullable|string|max:255',
        'ancestor_birth_date_5.*' => 'nullable|string|max:255',
        'ancestor_death_date_5.*' => 'nullable|string|max:255',
        'ancestor_birth_place_5.*' => 'nullable|string|max:255',
        'ancestor_occupation_5.*' => 'nullable|string|max:255',
        'ancestor_spouse_5.*' => 'nullable|string|max:255',
        'ancestor_children_count_5.*' => 'nullable|integer|min:0',
        'ancestor_gotra_5.*' => 'nullable|string|max:255',
        'ancestor_name_6.*' => 'nullable|string|max:255',
        'ancestor_birth_date_6.*' => 'nullable|string|max:255',
        'ancestor_death_date_6.*' => 'nullable|string|max:255',
        'ancestor_birth_place_6.*' => 'nullable|string|max:255',
        'ancestor_occupation_6.*' => 'nullable|string|max:255',
        'ancestor_spouse_6.*' => 'nullable|string|max:255',
        'ancestor_children_count_6.*' => 'nullable|integer|min:0',
        'ancestor_gotra_6.*' => 'nullable|string|max:255',
        'ancestor_name_7.*' => 'nullable|string|max:255',
        'ancestor_birth_date_7.*' => 'nullable|string|max:255',
        'ancestor_death_date_7.*' => 'nullable|string|max:255',
        'ancestor_birth_place_7.*' => 'nullable|string|max:255',
        'ancestor_occupation_7.*' => 'nullable|string|max:255',
        'ancestor_spouse_7.*' => 'nullable|string|max:255',
        'ancestor_children_count_7.*' => 'nullable|integer|min:0',
        'ancestor_gotra_7.*' => 'nullable|string|max:255',
    ], [
        // Hindi validation messages
        'photo.image' => 'फोटो एक वैध इमेज फाइल होनी चाहिए।',
        'photo.mimes' => 'फोटो JPG, JPEG या PNG फॉर्मेट में होनी चाहिए।',
        'photo.max' => 'फोटो का साइज 2MB से कम होना चाहिए।',
        'signature.image' => 'हस्ताक्षर एक वैध इमेज फाइल होना चाहिए।',
        'signature.mimes' => 'हस्ताक्षर JPG, JPEG या PNG फॉर्मेट में होना चाहिए।',
        'signature.max' => 'हस्ताक्षर का साइज 1MB से कम होना चाहिए।',
        'name.required' => 'नाम आवश्यक है।',
        'name.max' => 'नाम 255 अक्षरों से अधिक नहीं हो सकता।',
        'mobile_number.required' => 'मोबाइल नंबर आवश्यक है।',
        'mobile_number.digits' => 'मोबाइल नंबर 10 अंकों का होना चाहिए।',
        'mobile_number.starts_with' => 'मोबाइल नंबर 6, 7, 8 या 9 से शुरू होना चाहिए।',
        'mobile_number.unique' => 'यह मोबाइल नंबर पहले से पंजीकृत है।',
        'birth_date.required' => 'जन्म तिथि आवश्यक है।',
        'division_master_id.required' => 'संभाग चुनना आवश्यक है।',
        'district_master_id.required' => 'जिला चुनना आवश्यक है।',
        'vikaskhand_master_id.required' => 'विकासखंड चुनना आवश्यक है।',
        'email.required' => 'ईमेल पता आवश्यक है।',
        'email.email' => 'कृपया वैध ईमेल पता दर्ज करें।',
        'email.unique' => 'यह ईमेल पता पहले से पंजीकृत है।',
        'password.required' => 'पासवर्ड आवश्यक है।',
        'password.min' => 'पासवर्ड कम से कम 8 अक्षर का होना चाहिए।',
        'password.confirmed' => 'पासवर्ड की पुष्टि मेल नहीं खाती।',
        'membership_type.required' => 'सदस्यता का प्रकार चुनना आवश्यक है।',
        'member_name_signature.required' => 'सदस्य का नाम (हस्ताक्षर) आवश्यक है।',
        'mobile.required' => 'प्रस्तावक का मोबाइल नंबर आवश्यक है।',
        'mobile.digits' => 'प्रस्तावक का मोबाइल नंबर 10 अंकों का होना चाहिए।',
        'mobile.starts_with' => 'प्रस्तावक का मोबाइल नंबर 6, 7, 8 या 9 से शुरू होना चाहिए।',
        'declaration.required' => 'घोषणा स्वीकार करना आवश्यक है।',
        'declaration.accepted' => 'घोषणा स्वीकार करना आवश्यक है।',
        'family_members.integer' => 'परिवार के सदस्यों की संख्या एक संख्या होनी चाहिए।',
        'family_members.min' => 'परिवार में कम से कम 1 सदस्य होना चाहिए।',
    ]);

    // Handle file uploads
    if ($request->hasFile('photo')) {
        $validatedData['photo'] = $request->file('photo')->store('photos', 'public');
    }

    if ($request->hasFile('signature')) {
        $validatedData['signature'] = $request->file('signature')->store('signatures', 'public');
    }

    // Handle child details as JSON
    $children = [];
    if ($request->child_name && is_array($request->child_name)) {
        foreach ($request->child_name as $index => $name) {
            if (!empty($name)) {
                $children[] = [
                    'name' => $name,
                    'gender' => $request->child_gender[$index] ?? null,
                    'dob' => $request->child_dob[$index] ?? null,
                    'education' => $request->child_education[$index] ?? null,
                    'occupation' => $request->child_occupation[$index] ?? null,
                    'marital_status' => $request->child_marital_status[$index] ?? null,
                    'spouse_name' => $request->child_spouse_name[$index] ?? null,
                    'spouse_occupation' => $request->child_spouse_occupation[$index] ?? null,
                ];
            }
        }
    }
    $validatedData['children'] = $children;

    // Handle ancestor details as JSON - Process all generation levels
    $ancestors = [];

    // Process each generation level (1-7)
    for ($level = 1; $level <= 7; $level++) {
        $ancestorNameField = "ancestor_name_{$level}";
        $ancestorBirthDateField = "ancestor_birth_date_{$level}";
        $ancestorDeathDateField = "ancestor_death_date_{$level}";
        $ancestorBirthPlaceField = "ancestor_birth_place_{$level}";
        $ancestorOccupationField = "ancestor_occupation_{$level}";
        $ancestorSpouseField = "ancestor_spouse_{$level}";
        $ancestorChildrenCountField = "ancestor_children_count_{$level}";
        $ancestorGotraField = "ancestor_gotra_{$level}";

        if ($request->has($ancestorNameField) && is_array($request->$ancestorNameField)) {
            foreach ($request->$ancestorNameField as $index => $name) {
                if (!empty($name)) {
                    $ancestors[] = [
                        'generation_level' => $level,
                        'name' => $name,
                        'birth_date' => $request->{$ancestorBirthDateField}[$index] ?? null,
                        'death_date' => $request->{$ancestorDeathDateField}[$index] ?? null,
                        'birth_place' => $request->{$ancestorBirthPlaceField}[$index] ?? null,
                        'occupation' => $request->{$ancestorOccupationField}[$index] ?? null,
                        'spouse' => $request->{$ancestorSpouseField}[$index] ?? null,
                        'children_count' => $request->{$ancestorChildrenCountField}[$index] ?? null,
                        'gotra' => $request->{$ancestorGotraField}[$index] ?? null,
                    ];
                }
            }
        }
    }

    $validatedData['ancestors'] = $ancestors;

    // Set declaration as true (already validated)
    $validatedData['declaration'] = true;

    // Password will be automatically hashed by the model mutator
    // No need to hash it here as the model handles it

    // Save the membership record
    $membership = Membership::create($validatedData);

    // Redirect with success message and membership number
    return redirect()->route('sadasya.aavedan')->with([
        'success' => 'सदस्यता पंजीयन सफल रहा। स्वीकृति के बाद आप अपने ईमेल और पासवर्ड से लॉगिन कर सकेंगे।',
        'membership_number' => $membership->membership_number ?? $membership->id,
        'login_info' => 'लॉगिन: ' . $validatedData['email'] . ' (ईमेल) + आपका सेट किया गया पासवर्ड'
    ]);
}



    
    public function suchanaView()
    {
        // Only show approved memberships to the public
        $memberships = Membership::where('status', Membership::STATUS_APPROVED)
            ->with(['membershipVarg', 'educationQualification', 'officeMaster', 'departmentMaster'])
            ->latest()
            ->paginate(10);
        return view('sadasya.suchana', compact('memberships'));
    }

    /**
     * Show individual vaivahik panjiyan profile
     */
    public function showVaivahikPanjiyan($id)
    {
        $vaivahikData = VaivahikPanjiyan::findOrFail($id);

        // Check if user is logged in with membership guard
        $isLoggedIn = auth('member')->check();

        return view('sadasya.vaivahik-panjiyan-show', compact('vaivahikData', 'isLoggedIn'));
    }
}
