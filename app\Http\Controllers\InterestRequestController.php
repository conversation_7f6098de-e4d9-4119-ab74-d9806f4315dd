<?php

namespace App\Http\Controllers;

use App\Models\InterestRequest;
use App\Models\VaivahikPanjiyan;
use App\Models\Membership;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class InterestRequestController extends Controller
{
    /**
     * Submit an interest request
     */
    public function submit(Request $request, $profileId)
    {
        try {
            // Check authentication - try both guards
            $user = null;
            $userType = null;

            if (Auth::guard('vaivahik')->check()) {
                $user = Auth::guard('vaivahik')->user();
                $userType = 'vaivahik';
            } elseif (Auth::guard('member')->check()) {
                $user = Auth::guard('member')->user();
                $userType = 'member';
            }

        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'कृपया पहले लॉगिन करें।',
                'redirect' => route('vaivahik.login')
            ], 401);
        }

        // Check if user is approved (for members) or active (for vaivahik users)
        if ($userType === 'member' && $user->status !== Membership::STATUS_APPROVED) {
            return response()->json([
                'success' => false,
                'message' => 'केवल स्वीकृत सदस्य ही रुचि व्यक्त कर सकते हैं।'
            ], 403);
        }

        if ($userType === 'vaivahik' && (!$user->is_active || $user->status !== VaivahikPanjiyan::STATUS_APPROVED)) {
            return response()->json([
                'success' => false,
                'message' => 'केवल सक्रिय और स्वीकृत उपयोगकर्ता ही रुचि व्यक्त कर सकते हैं।'
            ], 403);
        }

        // Validate the profile exists
        $profile = VaivahikPanjiyan::findOrFail($profileId);

        // Check if trying to express interest in own profile
        $isOwnProfile = false;

        if ($userType === 'vaivahik') {
            // For vaivahik users, check if it's the same profile ID
            $isOwnProfile = $user->id === $profile->id;
        } elseif ($userType === 'member') {
            // For members, check if their mobile matches the profile mobile
            $isOwnProfile = $user->mobile_number === $profile->mobile;
        }

        if ($isOwnProfile) {
            return response()->json([
                'success' => false,
                'message' => 'आप अपनी प्रोफाइल में रुचि व्यक्त नहीं कर सकते।'
            ], 400);
        }

        // Check if request already exists
        $existingRequest = InterestRequest::where('requester_id', $user->id)
            ->where('requester_type', $userType)
            ->where('profile_id', $profileId)
            ->first();

        if ($existingRequest) {
            $statusMessage = match($existingRequest->status) {
                'pending' => 'आपका अनुरोध पहले से ही प्रतीक्षा में है।',
                'accepted' => 'आपका अनुरोध स्वीकार किया गया है।',
                'rejected' => 'आपका अनुरोध अस्वीकार किया गया था।',
                default => 'आपका अनुरोध पहले से ही मौजूद है।'
            };

            return response()->json([
                'success' => false,
                'message' => $statusMessage
            ], 409);
        }

        // Validate request data
        $validated = $request->validate([
            'message' => 'nullable|string|max:500'
        ]);

        // Create interest request
        $interestRequest = InterestRequest::create([
            'requester_id' => $user->id,
            'requester_type' => $userType,
            'profile_id' => $profileId,
            'message' => $validated['message'] ?? null,
            'status' => InterestRequest::STATUS_PENDING
        ]);

            return response()->json([
                'success' => true,
                'message' => 'आपका रुचि अनुरोध सफलतापूर्वक भेजा गया है।',
                'request_id' => $interestRequest->id
            ]);
        } catch (\Exception $e) {
            // Log the error for debugging
            \Log::error('Interest Request Error: ' . $e->getMessage(), [
                'profile_id' => $profileId,
                'user_id' => $user->id ?? null,
                'user_type' => $userType ?? null,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'कुछ गलत हुआ है। कृपया बाद में पुनः प्रयास करें।',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * Check if user can view contact details
     */
    public function checkContactAccess($profileId)
    {
        // Check authentication - try both guards
        $user = null;
        $userType = null;

        if (Auth::guard('vaivahik')->check()) {
            $user = Auth::guard('vaivahik')->user();
            $userType = 'vaivahik';
        } elseif (Auth::guard('member')->check()) {
            $user = Auth::guard('member')->user();
            $userType = 'member';
        }

        if (!$user) {
            return response()->json([
                'success' => false,
                'hasAccess' => false,
                'message' => 'कृपया पहले लॉगिन करें।'
            ]);
        }

        // Check if there's an accepted request
        $acceptedRequest = InterestRequest::where('requester_id', $user->id)
            ->where('requester_type', $userType)
            ->where('profile_id', $profileId)
            ->where('status', InterestRequest::STATUS_ACCEPTED)
            ->first();

        if ($acceptedRequest) {
            $profile = VaivahikPanjiyan::findOrFail($profileId);
            return response()->json([
                'success' => true,
                'hasAccess' => true,
                'contactInfo' => [
                    'mobile' => $profile->mobile,
                    'email' => $profile->owner_email,
                    'message' => 'संपर्क विवरण उपलब्ध है।'
                ]
            ]);
        }

        // Check if there's a pending request
        $pendingRequest = InterestRequest::where('requester_id', $user->id)
            ->where('requester_type', $userType)
            ->where('profile_id', $profileId)
            ->where('status', InterestRequest::STATUS_PENDING)
            ->first();

        if ($pendingRequest) {
            return response()->json([
                'success' => false,
                'hasAccess' => false,
                'message' => 'आपका अनुरोध प्रतीक्षा में है।',
                'status' => 'pending'
            ]);
        }

        return response()->json([
            'success' => false,
            'hasAccess' => false,
            'message' => 'संपर्क विवरण देखने के लिए रुचि व्यक्त करें।',
            'status' => 'no_request'
        ]);
    }

    /**
     * Get user's interest requests
     */
    public function myRequests()
    {
        if (!Auth::guard('member')->check()) {
            return redirect()->route('member.login');
        }

        $member = Auth::guard('member')->user();
        
        $requests = InterestRequest::where('requester_id', $member->id)
            ->with('profile')
            ->latest()
            ->paginate(10);

        return view('member.interest-requests', compact('requests'));
    }

    /**
     * Get requests received for user's profiles
     */
    public function receivedRequests()
    {
        if (!Auth::guard('member')->check()) {
            return redirect()->route('member.login');
        }

        $member = Auth::guard('member')->user();
        
        // Get profiles owned by this member (assuming mobile number matching)
        $memberProfiles = VaivahikPanjiyan::where('mobile', $member->mobile_number)->pluck('id');
        
        $requests = InterestRequest::whereIn('profile_id', $memberProfiles)
            ->with(['requester', 'profile'])
            ->latest()
            ->paginate(10);

        return view('member.received-requests', compact('requests'));
    }

    /**
     * Accept an interest request
     */
    public function accept($requestId)
    {
        if (!Auth::guard('member')->check()) {
            return response()->json(['success' => false, 'message' => 'Unauthorized'], 401);
        }

        $member = Auth::guard('member')->user();
        
        // Find request and verify ownership
        $request = InterestRequest::with('profile')->findOrFail($requestId);
        
        // Check if the profile belongs to the authenticated member
        if ($request->profile->mobile !== $member->mobile_number) {
            return response()->json(['success' => false, 'message' => 'Unauthorized'], 403);
        }

        $request->accept();

        return response()->json([
            'success' => true,
            'message' => 'अनुरोध स्वीकार किया गया।'
        ]);
    }

    /**
     * Reject an interest request
     */
    public function reject($requestId)
    {
        if (!Auth::guard('member')->check()) {
            return response()->json(['success' => false, 'message' => 'Unauthorized'], 401);
        }

        $member = Auth::guard('member')->user();
        
        // Find request and verify ownership
        $request = InterestRequest::with('profile')->findOrFail($requestId);
        
        // Check if the profile belongs to the authenticated member
        if ($request->profile->mobile !== $member->mobile_number) {
            return response()->json(['success' => false, 'message' => 'Unauthorized'], 403);
        }

        $request->reject();

        return response()->json([
            'success' => true,
            'message' => 'अनुरोध अस्वीकार किया गया।'
        ]);
    }
}
