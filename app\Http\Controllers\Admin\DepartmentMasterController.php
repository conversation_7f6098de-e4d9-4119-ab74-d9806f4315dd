<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\DepartmentMaster;
use Illuminate\Http\Request;

class DepartmentMasterController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('admin.auth');
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = DepartmentMaster::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('name_english', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('category', 'like', "%{$search}%")
                  ->orWhere('ministry', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        $departmentMasters = $query->ordered()->paginate(15);

        return view('admin.department-masters.index', compact('departmentMasters'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.department-masters.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:department_masters,name',
                'name_english' => 'nullable|string|max:255',
                'description' => 'nullable|string',
                'category' => 'nullable|string|max:255',
                'ministry' => 'nullable|string|max:255',
                'is_active' => 'boolean',
                'display_order' => 'nullable|integer|min:0',
            ]);

            $validated['is_active'] = $request->has('is_active');
            $validated['display_order'] = $validated['display_order'] ?? 0;

            DepartmentMaster::create($validated);

            return redirect()->route('admin.department-masters.index')
                           ->with('success', 'विभाग सफलतापूर्वक जोड़ा गया।');

        } catch (\Exception $e) {
            return back()->withInput()
                        ->with('error', 'विभाग जोड़ने में त्रुटि: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(DepartmentMaster $departmentMaster)
    {
        return view('admin.department-masters.show', compact('departmentMaster'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(DepartmentMaster $departmentMaster)
    {
        return view('admin.department-masters.edit', compact('departmentMaster'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, DepartmentMaster $departmentMaster)
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:department_masters,name,' . $departmentMaster->id,
                'name_english' => 'nullable|string|max:255',
                'description' => 'nullable|string',
                'category' => 'nullable|string|max:255',
                'ministry' => 'nullable|string|max:255',
                'is_active' => 'boolean',
                'display_order' => 'nullable|integer|min:0',
            ]);

            $validated['is_active'] = $request->has('is_active');
            $validated['display_order'] = $validated['display_order'] ?? 0;

            $departmentMaster->update($validated);

            return redirect()->route('admin.department-masters.index')
                           ->with('success', 'विभाग सफलतापूर्वक अपडेट किया गया।');

        } catch (\Exception $e) {
            return back()->withInput()
                        ->with('error', 'विभाग अपडेट करने में त्रुटि: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(DepartmentMaster $departmentMaster)
    {
        try {
            // Check if this department is being used
            $usageCount = $departmentMaster->memberships()->count() + 
                         $departmentMaster->vaivahikPanjiyans()->count();
            
            if ($usageCount > 0) {
                return back()->with('error', 'यह विभाग उपयोग में है, इसे हटाया नहीं जा सकता।');
            }

            $departmentMaster->delete();

            return redirect()->route('admin.department-masters.index')
                           ->with('success', 'विभाग सफलतापूर्वक हटाया गया।');

        } catch (\Exception $e) {
            return back()->with('error', 'विभाग हटाने में त्रुटि: ' . $e->getMessage());
        }
    }
}
