<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EkadashSadasyata extends Model
{
    use HasFactory;

    protected $table = 'ekadash_sadasyata';

    // Status constants
    public const STATUS_PENDING = 'pending';
    public const STATUS_APPROVED = 'approved';
    public const STATUS_REJECTED = 'rejected';

    protected $fillable = [
        'name', 'fathers_husband_name', 'mobile_number', 'birth_date',
        'marital_status', 'vivah_tithi', 'family_members', 'education',
        'course_stream_name', 'caste_details', 'vibhagiy_padnaam',
        'department_name', 'office', 'karyalay_ka_pata', 'vartaman_pata',
        'isthayi_pata', 'member_name_signature', 'mobile', 'proposer_address',
        'declaration', 'photo', 'signature', 'children', 'ancestors',
        'status', 'rejection_reason', 'vibhag', 'is_active', 'fee',
        'education_qualification_id', 'office_master_id', 'department_master_id',
        'yadav_varg_id', 'division_master_id', 'district_master_id',
        'vikaskhand_master_id'
    ];

    protected $casts = [
        'birth_date' => 'date',
        'vivah_tithi' => 'date',
        'declaration' => 'boolean',
        'is_active' => 'boolean',
        'children' => 'array',
        'ancestors' => 'array',
        'fee' => 'decimal:2',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->membership_number)) {
                $model->membership_number = self::generateMembershipNumber();
            }
        });
    }

    /**
     * Generate unique membership number
     */
    public static function generateMembershipNumber()
    {
        $lastMember = self::orderBy('id', 'desc')->first();
        $nextNumber = $lastMember ? $lastMember->id + 1 : 1;
        return 'EKDS' . str_pad($nextNumber, 6, '0', STR_PAD_LEFT);
    }

    /**
     * Check if the application is approved
     */
    public function isApproved()
    {
        return $this->status === self::STATUS_APPROVED;
    }

    /**
     * Check if the application is pending
     */
    public function isPending()
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Check if the application is rejected
     */
    public function isRejected()
    {
        return $this->status === self::STATUS_REJECTED;
    }

    /**
     * Relationship with EducationQualification
     */
    public function educationQualification()
    {
        return $this->belongsTo(EducationQualification::class);
    }

    /**
     * Relationship with OfficeMaster
     */
    public function officeMaster()
    {
        return $this->belongsTo(OfficeMaster::class);
    }

    /**
     * Relationship with DepartmentMaster
     */
    public function departmentMaster()
    {
        return $this->belongsTo(DepartmentMaster::class);
    }

    /**
     * Relationship with YadavVarg
     */
    public function yadavVarg()
    {
        return $this->belongsTo(YadavVarg::class, 'yadav_varg_id');
    }

    /**
     * Relationship with DivisionMaster
     */
    public function divisionMaster()
    {
        return $this->belongsTo(DivisionMaster::class, 'division_master_id');
    }

    /**
     * Relationship with DistrictMaster
     */
    public function districtMaster()
    {
        return $this->belongsTo(DistrictMaster::class, 'district_master_id');
    }

    /**
     * Relationship with VikaskhandMaster
     */
    public function vikaskhandMaster()
    {
        return $this->belongsTo(VikaskhandMaster::class, 'vikaskhand_master_id');
    }

    /**
     * Scope for approved members only
     */
    public function scopeApproved($query)
    {
        return $query->where('status', self::STATUS_APPROVED);
    }

    /**
     * Scope for pending members only
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * Scope for active members only
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
