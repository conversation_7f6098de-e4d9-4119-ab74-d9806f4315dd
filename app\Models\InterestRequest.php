<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InterestRequest extends Model
{
    use HasFactory;

    protected $fillable = [
        'requester_id',
        'requester_type',
        'profile_id',
        'status',
        'message',
        'response_message',
        'accepted_at',
        'rejected_at',
        'responded_at',
    ];

    protected $casts = [
        'accepted_at' => 'datetime',
        'rejected_at' => 'datetime',
        'responded_at' => 'datetime',
    ];

    // Status constants
    public const STATUS_PENDING = 'pending';
    public const STATUS_ACCEPTED = 'accepted';
    public const STATUS_REJECTED = 'rejected';

    /**
     * Get the user who made the request (can be Member or VaivahikPanjiyan user)
     */
    public function requester()
    {
        if ($this->requester_type === 'member') {
            return $this->belongsTo(Membership::class, 'requester_id')->first();
        } else {
            return $this->belongsTo(VaivahikPanjiyan::class, 'requester_id')->first();
        }
    }

    /**
     * Get the member who made the request (if it's a member)
     */
    public function memberRequester()
    {
        return $this->belongsTo(Membership::class, 'requester_id');
    }

    /**
     * Get the vaivahik user who made the request (if it's a vaivahik user)
     */
    public function vaivahikRequester()
    {
        return $this->belongsTo(VaivahikPanjiyan::class, 'requester_id');
    }

    /**
     * Get the member who made the request (if it's a member) - Legacy method
     */
    public function requesterMember()
    {
        return $this->belongsTo(Membership::class, 'requester_id');
    }

    /**
     * Get the vaivahik user who made the request (if it's a vaivahik user) - Legacy method
     */
    public function requesterVaivahik()
    {
        return $this->belongsTo(VaivahikPanjiyan::class, 'requester_id');
    }

    /**
     * Get the profile that was requested
     */
    public function profile()
    {
        return $this->belongsTo(VaivahikPanjiyan::class, 'profile_id');
    }

    /**
     * Check if request is pending
     */
    public function isPending()
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Check if request is accepted
     */
    public function isAccepted()
    {
        return $this->status === self::STATUS_ACCEPTED;
    }

    /**
     * Check if request is rejected
     */
    public function isRejected()
    {
        return $this->status === self::STATUS_REJECTED;
    }

    /**
     * Accept the interest request
     */
    public function accept()
    {
        $this->update([
            'status' => self::STATUS_ACCEPTED,
            'accepted_at' => now(),
        ]);
    }

    /**
     * Reject the interest request
     */
    public function reject()
    {
        $this->update([
            'status' => self::STATUS_REJECTED,
            'rejected_at' => now(),
        ]);
    }
}
