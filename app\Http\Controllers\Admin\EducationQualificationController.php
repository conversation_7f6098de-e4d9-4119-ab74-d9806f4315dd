<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\EducationQualification;
use Illuminate\Http\Request;

class EducationQualificationController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('admin.auth');
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = EducationQualification::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('name_english', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('level', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        $educationQualifications = $query->ordered()->paginate(15);

        return view('admin.education-qualifications.index', compact('educationQualifications'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.education-qualifications.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:education_qualifications,name',
                'name_english' => 'nullable|string|max:255',
                'description' => 'nullable|string',
                'level' => 'nullable|string|max:255',
                'is_active' => 'boolean',
                'display_order' => 'nullable|integer|min:0',
            ]);

            $validated['is_active'] = $request->has('is_active');
            $validated['display_order'] = $validated['display_order'] ?? 0;

            EducationQualification::create($validated);

            return redirect()->route('admin.education-qualifications.index')
                           ->with('success', 'शैक्षणिक योग्यता सफलतापूर्वक जोड़ी गई।');

        } catch (\Exception $e) {
            return back()->withInput()
                        ->with('error', 'शैक्षणिक योग्यता जोड़ने में त्रुटि: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(EducationQualification $educationQualification)
    {
        return view('admin.education-qualifications.show', compact('educationQualification'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(EducationQualification $educationQualification)
    {
        return view('admin.education-qualifications.edit', compact('educationQualification'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, EducationQualification $educationQualification)
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:education_qualifications,name,' . $educationQualification->id,
                'name_english' => 'nullable|string|max:255',
                'description' => 'nullable|string',
                'level' => 'nullable|string|max:255',
                'is_active' => 'boolean',
                'display_order' => 'nullable|integer|min:0',
            ]);

            $validated['is_active'] = $request->has('is_active');
            $validated['display_order'] = $validated['display_order'] ?? 0;

            $educationQualification->update($validated);

            return redirect()->route('admin.education-qualifications.index')
                           ->with('success', 'शैक्षणिक योग्यता सफलतापूर्वक अपडेट की गई।');

        } catch (\Exception $e) {
            return back()->withInput()
                        ->with('error', 'शैक्षणिक योग्यता अपडेट करने में त्रुटि: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(EducationQualification $educationQualification)
    {
        try {
            // Check if this qualification is being used
            $usageCount = $educationQualification->memberships()->count() + 
                         $educationQualification->vaivahikPanjiyans()->count();
            
            if ($usageCount > 0) {
                return back()->with('error', 'यह शैक्षणिक योग्यता उपयोग में है, इसे हटाया नहीं जा सकता।');
            }

            $educationQualification->delete();

            return redirect()->route('admin.education-qualifications.index')
                           ->with('success', 'शैक्षणिक योग्यता सफलतापूर्वक हटाई गई।');

        } catch (\Exception $e) {
            return back()->with('error', 'शैक्षणिक योग्यता हटाने में त्रुटि: ' . $e->getMessage());
        }
    }
}
