<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\PramukhPadadhikari;
use App\Models\DivisionMaster;
use App\Models\DistrictMaster;
use App\Models\VikaskhandMaster;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PramukhPadadhikariController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = PramukhPadadhikari::with(['division', 'district', 'vikaskhand']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('naam', 'like', "%{$search}%")
                  ->orWhere('sangathan_me_padnaam', 'like', "%{$search}%")
                  ->orWhere('vibhagiy_padnaam', 'like', "%{$search}%")
                  ->orWhere('vibhag_ka_naam', 'like', "%{$search}%")
                  ->orWhere('mobile_number', 'like', "%{$search}%");
            });
        }

        // Status filter
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->active();
            } elseif ($request->status === 'inactive') {
                $query->inactive();
            }
        }

        $pramukhPadadhikaris = $query->orderBy('naam')->paginate(15);

        return view('admin.pramukh-padadhikaris.index', compact('pramukhPadadhikaris'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $divisions = DivisionMaster::active()->ordered()->get();
        $districts = DistrictMaster::active()->ordered()->get();
        $vikaskhands = VikaskhandMaster::active()->ordered()->get();

        return view('admin.pramukh-padadhikaris.create', compact('divisions', 'districts', 'vikaskhands'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            // Debug: Log the request data
            
            Log::info('Pramukh Padadhikari Store Request:', $request->all());

            $validated = $request->validate([
                'state_name' => 'required|string|max:255',
                'division_code' => 'nullable|string|exists:division_masters,division_code',
                'district_lgd_code' => 'nullable|string|exists:district_masters,district_lgd_code',
                'vikaskhand_lgd_code' => 'nullable|string|exists:vikaskhand_masters,sub_district_lgd_code',
                'naam' => 'required|string|max:255',
                'sangathan_me_padnaam' => 'required|string|max:255',
                'vibhagiy_padnaam' => 'nullable|string|max:255',
                'vibhag_ka_naam' => 'nullable|string|max:255',
                'vartaman_pata' => 'required|string',
                'isthayi_pata' => 'nullable|string',
                'sanchipt_vishesh' => 'nullable|string',
                'sadasyata_kramank_evam_prakar' => 'nullable|string|max:255',
                'mobile_number' => 'required|string|regex:/^[0-9]{10}$/|unique:pramukh_padadhikaris,mobile_number',
                'photo' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
                'remark' => 'nullable|string',
                'active' => 'boolean',
            ]);

            $validated['active'] = $request->has('active');

            // Set default state_name if not provided
            if (empty($validated['state_name'])) {
                $validated['state_name'] = 'छत्तीसगढ़';
            }

            // Handle photo upload
            if ($request->hasFile('photo') && $request->file('photo')->isValid()) {
                $validated['photo'] = $request->file('photo')->store('pramukh-padadhikaris/photos', 'public');
            }



            Log::info('Validated data:', $validated);

            $pramukhPadadhikari = PramukhPadadhikari::create($validated);

            Log::info('Created Pramukh Padadhikari:', $pramukhPadadhikari->toArray());

            return redirect()
                ->route('admin.pramukh-padadhikaris.index')
                ->with('success', 'प्रमुख पदाधिकारी सफलतापूर्वक जोड़ा गया।');
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error('Validation Error:', $e->errors());
            return redirect()->back()
                ->withInput()
                ->withErrors($e->errors())
                ->with('error', 'कृपया सभी आवश्यक फील्ड भरें।');
        } catch (\Exception $e) {
            Log::error('Store Error:', ['message' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            return redirect()->back()
                ->withInput()
                ->withErrors(['error' => 'प्रमुख पदाधिकारी जोड़ने में त्रुटि: ' . $e->getMessage()]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(PramukhPadadhikari $pramukhPadadhikari)
    {
        return view('admin.pramukh-padadhikaris.show', compact('pramukhPadadhikari'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(PramukhPadadhikari $pramukhPadadhikari)
    {
        $divisions = DivisionMaster::active()->ordered()->get();
        $districts = DistrictMaster::active()->ordered()->get();
        $vikaskhands = VikaskhandMaster::active()->ordered()->get();

        return view('admin.pramukh-padadhikaris.edit', compact('pramukhPadadhikari', 'divisions', 'districts', 'vikaskhands'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, PramukhPadadhikari $pramukhPadadhikari)
    {
        try {
            $validated = $request->validate([
                'state_name' => 'required|string|max:255',
                'division_code' => 'nullable|string|exists:division_masters,division_code',
                'district_lgd_code' => 'nullable|string|exists:district_masters,district_lgd_code',
                'vikaskhand_lgd_code' => 'nullable|string|exists:vikaskhand_masters,sub_district_lgd_code',
                'naam' => 'required|string|max:255',
                'sangathan_me_padnaam' => 'required|string|max:255',
                'vibhagiy_padnaam' => 'nullable|string|max:255',
                'vibhag_ka_naam' => 'nullable|string|max:255',
                'vartaman_pata' => 'required|string',
                'isthayi_pata' => 'nullable|string',
                'sanchipt_vishesh' => 'nullable|string',
                'sadasyata_kramank_evam_prakar' => 'nullable|string|max:255',
                'mobile_number' => 'required|string|regex:/^[0-9]{10}$/|unique:pramukh_padadhikaris,mobile_number,' . $pramukhPadadhikari->id,
                'photo' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
                'remark' => 'nullable|string',
                'active' => 'boolean',
            ]);

            $validated['active'] = $request->has('active');

            // Set default state_name if not provided
            if (empty($validated['state_name'])) {
                $validated['state_name'] = 'छत्तीसगढ़';
            }

            // Handle photo upload
            if ($request->hasFile('photo') && $request->file('photo')->isValid()) {
                // Delete old photo
                $pramukhPadadhikari->deletePhotoFile();

                // Store new photo
                $validated['photo'] = $request->file('photo')->store('pramukh-padadhikaris/photos', 'public');
            }



            $pramukhPadadhikari->update($validated);

            return redirect()
                ->route('admin.pramukh-padadhikaris.index')
                ->with('success', 'प्रमुख पदाधिकारी सफलतापूर्वक अपडेट किया गया।');
        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->withErrors(['error' => 'Failed to update Pramukh Padadhikari: ' . $e->getMessage()]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(PramukhPadadhikari $pramukhPadadhikari)
    {
        try {
            // Delete photo file
            $pramukhPadadhikari->deletePhotoFile();

            $pramukhPadadhikari->delete();

            return redirect()
                ->route('admin.pramukh-padadhikaris.index')
                ->with('success', 'Pramukh Padadhikari deleted successfully');
        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['error' => 'Failed to delete Pramukh Padadhikari: ' . $e->getMessage()]);
        }
    }

    /**
     * Toggle the active status of the specified resource.
     */
    public function toggleStatus(PramukhPadadhikari $pramukhPadadhikari)
    {
        try {
            $pramukhPadadhikari->update(['active' => !$pramukhPadadhikari->active]);

            $status = $pramukhPadadhikari->active ? 'activated' : 'deactivated';

            return redirect()
                ->route('admin.pramukh-padadhikaris.index')
                ->with('success', "Pramukh Padadhikari {$status} successfully");
        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['error' => 'Failed to update status: ' . $e->getMessage()]);
        }
    }

    /**
     * Get districts by division code for AJAX requests.
     */
    public function getDistrictsByDivision(Request $request)
    {
        $divisionCode = $request->get('division_code');

        if (!$divisionCode) {
            return response()->json([]);
        }

        $districts = DistrictMaster::where('division_code', $divisionCode)
                                  ->active()
                                  ->ordered()
                                  ->get(['district_lgd_code', 'district_name_hin', 'district_name_eng']);

        return response()->json($districts);
    }

    /**
     * Get vikaskhands by district code for AJAX requests.
     */
    public function getVikaskhandsByDistrict(Request $request)
    {
        $districtLgdCode = $request->get('district_lgd_code');

        if (!$districtLgdCode) {
            return response()->json([]);
        }

        $vikaskhands = VikaskhandMaster::where('district_lgd_code', $districtLgdCode)
                                      ->active()
                                      ->ordered()
                                      ->get(['sub_district_lgd_code', 'sub_district_name_hin', 'sub_district_name_eng']);

        return response()->json($vikaskhands);
    }

    /**
     * Show Pramukh Padadhikaris grouped by Sambhag (Division).
     * Only shows records where division is selected but district and vikaskhand are NULL.
     */
    public function sambhagWise(Request $request)
    {
        $query = PramukhPadadhikari::with(['division', 'district', 'vikaskhand'])
                                  ->whereNotNull('division_code')
                                  ->whereNull('district_lgd_code')
                                  ->whereNull('vikaskhand_lgd_code');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('naam', 'like', "%{$search}%")
                  ->orWhere('sangathan_me_padnaam', 'like', "%{$search}%")
                  ->orWhere('mobile_number', 'like', "%{$search}%")
                  ->orWhereHas('division', function ($divQuery) use ($search) {
                      $divQuery->where('division_name_hin', 'like', "%{$search}%")
                               ->orWhere('division_name_eng', 'like', "%{$search}%");
                  });
            });
        }

        // Division filter
        if ($request->filled('division_code')) {
            $query->where('division_code', $request->division_code);
        }

        // Status filter
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->where('active', true);
            } elseif ($request->status === 'inactive') {
                $query->where('active', false);
            }
        }

        $pramukhPadadhikaris = $query->orderBy('division_code')
                                    ->orderBy('naam')
                                    ->paginate(15);

        $divisions = DivisionMaster::active()->ordered()->get();

        return view('admin.pramukh-padadhikaris.sambhag-wise', compact('pramukhPadadhikaris', 'divisions'));
    }

    /**
     * Show Pramukh Padadhikaris grouped by Jila (District).
     * Only shows records where district is selected but vikaskhand is NULL.
     */
    public function jilaWise(Request $request)
    {
        $query = PramukhPadadhikari::with(['division', 'district', 'vikaskhand'])
                                  ->whereNotNull('district_lgd_code')
                                  ->whereNull('vikaskhand_lgd_code');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('naam', 'like', "%{$search}%")
                  ->orWhere('sangathan_me_padnaam', 'like', "%{$search}%")
                  ->orWhere('mobile_number', 'like', "%{$search}%")
                  ->orWhereHas('district', function ($distQuery) use ($search) {
                      $distQuery->where('district_name_hin', 'like', "%{$search}%")
                               ->orWhere('district_name_eng', 'like', "%{$search}%");
                  });
            });
        }

        // Division filter
        if ($request->filled('division_code')) {
            $query->where('division_code', $request->division_code);
        }

        // District filter
        if ($request->filled('district_lgd_code')) {
            $query->where('district_lgd_code', $request->district_lgd_code);
        }

        // Status filter
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->where('active', true);
            } elseif ($request->status === 'inactive') {
                $query->where('active', false);
            }
        }

        $pramukhPadadhikaris = $query->orderBy('district_lgd_code')
                                    ->orderBy('naam')
                                    ->paginate(15);

        $divisions = DivisionMaster::active()->ordered()->get();
        $districts = DistrictMaster::active()->ordered()->get();

        return view('admin.pramukh-padadhikaris.jila-wise', compact('pramukhPadadhikaris', 'divisions', 'districts'));
    }

    /**
     * Show Pramukh Padadhikaris grouped by Vikaskhand.
     */
    public function vikaskhandWise(Request $request)
    {
        $query = PramukhPadadhikari::with(['division', 'district', 'vikaskhand'])
                                  ->whereNotNull('vikaskhand_lgd_code');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('naam', 'like', "%{$search}%")
                  ->orWhere('sangathan_me_padnaam', 'like', "%{$search}%")
                  ->orWhere('mobile_number', 'like', "%{$search}%")
                  ->orWhereHas('vikaskhand', function ($vikQuery) use ($search) {
                      $vikQuery->where('sub_district_name_hin', 'like', "%{$search}%")
                               ->orWhere('sub_district_name_eng', 'like', "%{$search}%");
                  });
            });
        }

        // Division filter
        if ($request->filled('division_code')) {
            $query->where('division_code', $request->division_code);
        }

        // District filter
        if ($request->filled('district_lgd_code')) {
            $query->where('district_lgd_code', $request->district_lgd_code);
        }

        // Vikaskhand filter
        if ($request->filled('vikaskhand_lgd_code')) {
            $query->where('vikaskhand_lgd_code', $request->vikaskhand_lgd_code);
        }

        // Status filter
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->where('active', true);
            } elseif ($request->status === 'inactive') {
                $query->where('active', false);
            }
        }

        $pramukhPadadhikaris = $query->orderBy('vikaskhand_lgd_code')
                                    ->orderBy('naam')
                                    ->paginate(15);

        $divisions = DivisionMaster::active()->ordered()->get();
        $districts = DistrictMaster::active()->ordered()->get();
        $vikaskhands = VikaskhandMaster::active()->ordered()->get();

        return view('admin.pramukh-padadhikaris.vikaskhand-wise', compact('pramukhPadadhikaris', 'divisions', 'districts', 'vikaskhands'));
    }
}
