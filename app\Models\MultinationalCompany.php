<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class MultinationalCompany extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'naam',
        'pita_ka_naam',
        'pata',
        'umra',
        'ahartayen',
        'company_name',
        'location',
        'mobile_number',
        'package',
        'biodata',
        'media_link',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'umra' => 'integer',
    ];

    /**
     * Get biodata file URL if exists
     */
    public function getBiodataUrlAttribute()
    {
        if ($this->biodata && Storage::exists($this->biodata)) {
            return Storage::url($this->biodata);
        }
        return null;
    }

    /**
     * Check if biodata file exists
     */
    public function biodataExists()
    {
        return $this->biodata && Storage::exists($this->biodata);
    }

    /**
     * Delete biodata file from storage
     */
    public function deleteBiodataFile()
    {
        if ($this->biodataExists()) {
            Storage::delete($this->biodata);
        }
    }

    /**
     * Get biodata file name
     */
    public function getBiodataFileNameAttribute()
    {
        if ($this->biodata) {
            return basename($this->biodata);
        }
        return null;
    }

    /**
     * Get formatted media link for embedding
     */
    public function getFormattedMediaLinkAttribute()
    {
        if (!$this->media_link) {
            return null;
        }

        // Convert YouTube links to embed format
        if (preg_match('/(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]+)/', $this->media_link, $matches)) {
            return 'https://www.youtube.com/embed/' . $matches[1];
        }

        return $this->media_link;
    }

    /**
     * Check if media link is a YouTube video
     */
    public function isYouTubeVideo()
    {
        return preg_match('/(?:youtube\.com|youtu\.be)/', $this->media_link);
    }

    /**
     * Boot method to handle model events
     */
    protected static function boot()
    {
        parent::boot();

        // Delete biodata file when model is deleted
        static::deleting(function ($company) {
            $company->deleteBiodataFile();
        });
    }
}
