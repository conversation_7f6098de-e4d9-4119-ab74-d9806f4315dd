<?php declare(strict_types=1);
/*
 * This file is part of phpunit/php-code-coverage.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace <PERSON><PERSON><PERSON><PERSON><PERSON>\CodeCoverage\Driver;

use function sprintf;
use RuntimeException;
use <PERSON><PERSON><PERSON><PERSON>n\CodeCoverage\Exception;

final class WriteOperationFailedException extends RuntimeException implements Exception
{
    public function __construct(string $path)
    {
        parent::__construct(sprintf('Cannot write to "%s"', $path));
    }
}
