/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./resources/**/*.blade.php",
    "./resources/**/*.js",
    "./resources/**/*.vue",
  ],
  theme: {
    extend: {
      colors: {
        'navy': {
          50: '#eef1f8',
          100: '#d8e0f0',
          200: '#b1c2e1',
          300: '#89a3d2',
          400: '#6285c3',
          500: '#4a6fb1',
          600: '#3a5a8e',
          700: '#2b436a',
          800: '#1d2d47',
          900: '#0a1f44',
        },
        'saffron': {
          50: '#fff8ee',
          100: '#ffefd7',
          200: '#ffd8a8',
          300: '#ffc47a',
          400: '#ffaf4c',
          500: '#ff9933',
          600: '#ff7b00',
          700: '#cc6200',
          800: '#994a00',
          900: '#663100',
        },
        'green': {
          50: '#e6f2eb',
          100: '#cce5d7',
          200: '#99cbb0',
          300: '#66b188',
          400: '#339761',
          500: '#007d39',
          600: '#00642e',
          700: '#004b22',
          800: '#003217',
          900: '#0a5d00',
        },
        'maroon': {
          50: '#fcf2f4',
          100: '#f5d7dd',
          200: '#eba9b7',
          300: '#e17b91',
          400: '#d84d6c',
          500: '#c22a47',
          600: '#9c2239',
          700: '#761a2b',
          800: '#50111d',
          900: '#2a090e',
        },
      },
      fontFamily: {
        'hindi': ['Hind', 'Noto Sans Devanagari', 'sans-serif'],
        'headings': ['Poppins', 'Noto Sans Devanagari', 'sans-serif'],
      },
      borderRadius: {
        'xl': '1rem',
        '2xl': '1.5rem',
        '3xl': '2rem',
      },
      boxShadow: {
        'soft': '0 4px 15px rgba(0, 0, 0, 0.05)',
        'card': '0 10px 25px rgba(0, 0, 0, 0.05)',
        'nav': '0 2px 10px rgba(0, 0, 0, 0.08)',
        'elevated': '0 12px 28px rgba(0, 0, 0, 0.12)',
        'inner-glow': 'inset 0 2px 10px rgba(255, 153, 51, 0.15)',
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-in': 'slideIn 0.4s ease-out',
        'scale': 'scale 0.3s ease-in-out',
        'bounce-in': 'bounceIn 0.6s ease-out',
        'float': 'float 3s ease-in-out infinite',
        'pulse-soft': 'pulseSoft 3s ease-in-out infinite',
        'marquee-up': 'marqueeUp 15s linear infinite',
        'marquee-up-slow': 'marqueeUpSlow 25s linear infinite',
        'marquee-up-fast': 'marqueeUpFast 10s linear infinite',
        'marquee-up-extra-slow': 'marqueeUpExtraSlow 30s linear infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideIn: {
          '0%': { transform: 'translateY(20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        scale: {
          '0%': { transform: 'scale(1)' },
          '50%': { transform: 'scale(1.05)' },
          '100%': { transform: 'scale(1)' },
        },
        bounceIn: {
          '0%': { transform: 'scale(0.8)', opacity: '0' },
          '50%': { transform: 'scale(1.05)', opacity: '0.8' },
          '70%': { transform: 'scale(0.95)', opacity: '1' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
        float: {
          '0%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' },
          '100%': { transform: 'translateY(0px)' },
        },
        pulseSoft: {
          '0%, 100%': { opacity: '1' },
          '50%': { opacity: '0.8' },
        },
        marqueeUp: {
          '0%': { transform: 'translateY(0%)' },
          '100%': { transform: 'translateY(-50%)' },
        },
        marqueeUpSlow: {
          '0%': { transform: 'translateY(0%)' },
          '100%': { transform: 'translateY(-50%)' },
        },
        marqueeUpFast: {
          '0%': { transform: 'translateY(0%)' },
          '100%': { transform: 'translateY(-50%)' },
        },
        marqueeUpExtraSlow: {
          '0%': { transform: 'translateY(0%)' },
          '100%': { transform: 'translateY(-50%)' },
        },
      },
      backgroundImage: {
        'pattern-light': "url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f1f5f9' fill-opacity='0.6'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")",
        'pattern-saffron': "url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ff9933' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")",
        'gradient-radial': 'radial-gradient(ellipse at center, var(--tw-gradient-stops))',
        'gradient-conic': 'conic-gradient(from 225deg at 50% 50%, var(--tw-gradient-stops))'
      },
      textShadow: {
        'md': '0 2px 4px rgba(0, 0, 0, 0.1)',
        'lg': '0 3px 6px rgba(0, 0, 0, 0.15)',
      },
    },
  },
  plugins: [
    // Add custom plugin for text-shadow
    function({ addUtilities }) {
      const newUtilities = {
        '.text-shadow-md': {
          textShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
        },
        '.text-shadow-lg': {
          textShadow: '0 3px 6px rgba(0, 0, 0, 0.15)',
        },
        '.text-shadow-none': {
          textShadow: 'none',
        },
      }
      addUtilities(newUtilities)
    },
  ],
}