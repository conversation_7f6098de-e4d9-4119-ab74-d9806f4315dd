<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\DistrictMaster;

class DistrictMasterSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $districts = [
            // Lucknow Division
            [
                'district_lgd_code' => '395',
                'district_name_eng' => 'Lucknow',
                'district_name_hin' => 'लखनऊ',
                'district_short_name' => 'LKO',
                'division_code' => 'LKO',
                'is_active' => true,
                'display_order' => 1,
            ],
            [
                'district_lgd_code' => '396',
                'district_name_eng' => 'Unnao',
                'district_name_hin' => 'उन्नाव',
                'district_short_name' => 'UNN',
                'division_code' => 'LKO',
                'is_active' => true,
                'display_order' => 2,
            ],
            [
                'district_lgd_code' => '397',
                'district_name_eng' => 'Rae Bareli',
                'district_name_hin' => 'रायबरेली',
                'district_short_name' => 'RBL',
                'division_code' => 'LKO',
                'is_active' => true,
                'display_order' => 3,
            ],
            [
                'district_lgd_code' => '398',
                'district_name_eng' => 'Sitapur',
                'district_name_hin' => 'सीतापुर',
                'district_short_name' => 'STP',
                'division_code' => 'LKO',
                'is_active' => true,
                'display_order' => 4,
            ],
            [
                'district_lgd_code' => '399',
                'district_name_eng' => 'Hardoi',
                'district_name_hin' => 'हरदोई',
                'district_short_name' => 'HRD',
                'division_code' => 'LKO',
                'is_active' => true,
                'display_order' => 5,
            ],
            [
                'district_lgd_code' => '400',
                'district_name_eng' => 'Lakhimpur Kheri',
                'district_name_hin' => 'लखीमपुर खीरी',
                'district_short_name' => 'LKH',
                'division_code' => 'LKO',
                'is_active' => true,
                'display_order' => 6,
            ],

            // Kanpur Division
            [
                'district_lgd_code' => '401',
                'district_name_eng' => 'Kanpur Nagar',
                'district_name_hin' => 'कानपुर नगर',
                'district_short_name' => 'KNP',
                'division_code' => 'KNP',
                'is_active' => true,
                'display_order' => 7,
            ],
            [
                'district_lgd_code' => '402',
                'district_name_eng' => 'Kanpur Dehat',
                'district_name_hin' => 'कानपुर देहात',
                'district_short_name' => 'KND',
                'division_code' => 'KNP',
                'is_active' => true,
                'display_order' => 8,
            ],
            [
                'district_lgd_code' => '403',
                'district_name_eng' => 'Fatehpur',
                'district_name_hin' => 'फतेहपुर',
                'district_short_name' => 'FTP',
                'division_code' => 'KNP',
                'is_active' => true,
                'display_order' => 9,
            ],
            [
                'district_lgd_code' => '404',
                'district_name_eng' => 'Auraiya',
                'district_name_hin' => 'औरैया',
                'district_short_name' => 'AUR',
                'division_code' => 'KNP',
                'is_active' => true,
                'display_order' => 10,
            ],
            [
                'district_lgd_code' => '405',
                'district_name_eng' => 'Etawah',
                'district_name_hin' => 'इटावा',
                'district_short_name' => 'ETW',
                'division_code' => 'KNP',
                'is_active' => true,
                'display_order' => 11,
            ],

            // Agra Division
            [
                'district_lgd_code' => '406',
                'district_name_eng' => 'Agra',
                'district_name_hin' => 'आगरा',
                'district_short_name' => 'AGR',
                'division_code' => 'AGR',
                'is_active' => true,
                'display_order' => 12,
            ],
            [
                'district_lgd_code' => '407',
                'district_name_eng' => 'Firozabad',
                'district_name_hin' => 'फिरोजाबाद',
                'district_short_name' => 'FRZ',
                'division_code' => 'AGR',
                'is_active' => true,
                'display_order' => 13,
            ],
            [
                'district_lgd_code' => '408',
                'district_name_eng' => 'Mainpuri',
                'district_name_hin' => 'मैनपुरी',
                'district_short_name' => 'MNP',
                'division_code' => 'AGR',
                'is_active' => true,
                'display_order' => 14,
            ],
            [
                'district_lgd_code' => '409',
                'district_name_eng' => 'Mathura',
                'district_name_hin' => 'मथुरा',
                'district_short_name' => 'MTH',
                'division_code' => 'AGR',
                'is_active' => true,
                'display_order' => 15,
            ],

            // Meerut Division
            [
                'district_lgd_code' => '410',
                'district_name_eng' => 'Meerut',
                'district_name_hin' => 'मेरठ',
                'district_short_name' => 'MRT',
                'division_code' => 'MRT',
                'is_active' => true,
                'display_order' => 16,
            ],
            [
                'district_lgd_code' => '411',
                'district_name_eng' => 'Ghaziabad',
                'district_name_hin' => 'गाजियाबाद',
                'district_short_name' => 'GZB',
                'division_code' => 'MRT',
                'is_active' => true,
                'display_order' => 17,
            ],
            [
                'district_lgd_code' => '412',
                'district_name_eng' => 'Gautam Buddha Nagar',
                'district_name_hin' => 'गौतम बुद्ध नगर',
                'district_short_name' => 'GBN',
                'division_code' => 'MRT',
                'is_active' => true,
                'display_order' => 18,
            ],
            [
                'district_lgd_code' => '413',
                'district_name_eng' => 'Bulandshahr',
                'district_name_hin' => 'बुलंदशहर',
                'district_short_name' => 'BLD',
                'division_code' => 'MRT',
                'is_active' => true,
                'display_order' => 19,
            ],
            [
                'district_lgd_code' => '414',
                'district_name_eng' => 'Hapur',
                'district_name_hin' => 'हापुड़',
                'district_short_name' => 'HPR',
                'division_code' => 'MRT',
                'is_active' => true,
                'display_order' => 20,
            ],

            // Varanasi Division
            [
                'district_lgd_code' => '415',
                'district_name_eng' => 'Varanasi',
                'district_name_hin' => 'वाराणसी',
                'district_short_name' => 'VNS',
                'division_code' => 'VNS',
                'is_active' => true,
                'display_order' => 21,
            ],
            [
                'district_lgd_code' => '416',
                'district_name_eng' => 'Chandauli',
                'district_name_hin' => 'चंदौली',
                'district_short_name' => 'CHD',
                'division_code' => 'VNS',
                'is_active' => true,
                'display_order' => 22,
            ],
            [
                'district_lgd_code' => '417',
                'district_name_eng' => 'Jaunpur',
                'district_name_hin' => 'जौनपुर',
                'district_short_name' => 'JNP',
                'division_code' => 'VNS',
                'is_active' => true,
                'display_order' => 23,
            ],
            [
                'district_lgd_code' => '418',
                'district_name_eng' => 'Ghazipur',
                'district_name_hin' => 'गाजीपुर',
                'district_short_name' => 'GZP',
                'division_code' => 'VNS',
                'is_active' => true,
                'display_order' => 24,
            ],
        ];

        foreach ($districts as $district) {
            DistrictMaster::updateOrCreate(
                ['district_lgd_code' => $district['district_lgd_code']],
                $district
            );
        }
    }
}
