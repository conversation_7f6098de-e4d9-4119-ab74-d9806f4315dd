<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pramukh_padadhikaris', function (Blueprint $table) {
            // Check and add new master data foreign keys only if they don't exist
            if (!Schema::hasColumn('pramukh_padadhikaris', 'division_code')) {
                $table->string('division_code')->nullable()->after('id');
            }
            if (!Schema::hasColumn('pramukh_padadhikaris', 'district_lgd_code')) {
                $table->string('district_lgd_code')->nullable()->after('division_code');
            }
            if (!Schema::hasColumn('pramukh_padadhikaris', 'vikaskhand_lgd_code')) {
                $table->string('vikaskhand_lgd_code')->nullable()->after('district_lgd_code');
            }

            // Add new fields with Hindi names (keeping old fields for now)
            if (!Schema::hasColumn('pramukh_padadhikaris', 'naam')) {
                $table->string('naam')->nullable()->after('vikaskhand_lgd_code');
            }
            if (!Schema::hasColumn('pramukh_padadhikaris', 'sangathan_me_padnaam')) {
                $table->string('sangathan_me_padnaam')->nullable()->after('naam');
            }
            if (!Schema::hasColumn('pramukh_padadhikaris', 'vibhagiy_padnaam')) {
                $table->string('vibhagiy_padnaam')->nullable()->after('sangathan_me_padnaam');
            }
            if (!Schema::hasColumn('pramukh_padadhikaris', 'vibhag_ka_naam')) {
                $table->string('vibhag_ka_naam')->nullable()->after('vibhagiy_padnaam');
            }
            if (!Schema::hasColumn('pramukh_padadhikaris', 'vartaman_pata')) {
                $table->text('vartaman_pata')->nullable()->after('vibhag_ka_naam');
            }
            if (!Schema::hasColumn('pramukh_padadhikaris', 'isthayi_pata')) {
                $table->text('isthayi_pata')->nullable()->after('vartaman_pata');
            }
            if (!Schema::hasColumn('pramukh_padadhikaris', 'sanchipt_vishesh')) {
                $table->text('sanchipt_vishesh')->nullable()->after('isthayi_pata');
            }
            if (!Schema::hasColumn('pramukh_padadhikaris', 'sadasyata_kramank_evam_prakar')) {
                $table->string('sadasyata_kramank_evam_prakar')->nullable()->after('sanchipt_vishesh');
            }
            if (!Schema::hasColumn('pramukh_padadhikaris', 'remark')) {
                $table->text('remark')->nullable()->after('sadasyata_kramank_evam_prakar');
            }
        });

        // Add foreign key constraints and indexes in a separate schema operation
        Schema::table('pramukh_padadhikaris', function (Blueprint $table) {
            // Add foreign key constraints only if they don't exist
            try {
                $table->foreign('division_code')->references('division_code')->on('division_masters')->onDelete('set null');
            } catch (\Exception $e) {
                // Foreign key already exists
            }
            try {
                $table->foreign('district_lgd_code')->references('district_lgd_code')->on('district_masters')->onDelete('set null');
            } catch (\Exception $e) {
                // Foreign key already exists
            }
            try {
                $table->foreign('vikaskhand_lgd_code')->references('sub_district_lgd_code')->on('vikaskhand_masters')->onDelete('set null');
            } catch (\Exception $e) {
                // Foreign key already exists
            }

            // Add indexes for better performance
            try {
                $table->index('division_code');
            } catch (\Exception $e) {
                // Index already exists
            }
            try {
                $table->index('district_lgd_code');
            } catch (\Exception $e) {
                // Index already exists
            }
            try {
                $table->index('vikaskhand_lgd_code');
            } catch (\Exception $e) {
                // Index already exists
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pramukh_padadhikaris', function (Blueprint $table) {
            // Drop foreign key constraints
            $table->dropForeign(['division_code']);
            $table->dropForeign(['district_lgd_code']);
            $table->dropForeign(['vikaskhand_lgd_code']);

            // Drop indexes
            $table->dropIndex(['division_code']);
            $table->dropIndex(['district_lgd_code']);
            $table->dropIndex(['vikaskhand_lgd_code']);

            // Drop new fields
            $table->dropColumn([
                'division_code',
                'district_lgd_code',
                'vikaskhand_lgd_code',
                'naam',
                'sangathan_me_padnaam',
                'vibhagiy_padnaam',
                'vibhag_ka_naam',
                'vartaman_pata',
                'isthayi_pata',
                'sanchipt_vishesh',
                'sadasyata_kramank_evam_prakar',
                'remark'
            ]);
        });
    }
};
