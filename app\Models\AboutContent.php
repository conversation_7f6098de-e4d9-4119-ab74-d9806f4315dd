<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AboutContent extends Model
{
    use HasFactory;
    
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'page_key',
        'section_key',
        'title',
        'content',
        'image_path',
        'display_order',
        'is_published',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_published' => 'boolean',
        'display_order' => 'integer',
    ];
    
    /**
     * Constants for page keys to avoid magic strings
     */
    const PAGE_UDDESHYA = 'uddeshya-aur-itihas';
    const PAGE_PRAMUKH = 'pramukh-padadhikari';
    const PAGE_SANRACHNA = 'sanrachna-karyapranali';
    
    /**
     * Get content by page key, ordered by display_order
     */
    public static function getContentByPage($pageKey)
    {
        return self::where('page_key', $pageKey)
            ->where('is_published', true)
            ->orderBy('display_order')
            ->get();
    }
    
    /**
     * Get available pages for dropdown options
     */
    public static function getPageOptions()
    {
        return [
            self::PAGE_UDDESHYA => 'उद्देश्य और इतिहास',
            self::PAGE_PRAMUKH => 'प्रमुख पदाधिकारी',
            self::PAGE_SANRACHNA => 'संरचना और कार्यप्रणाली',
        ];
    }

    /**
     * Mutator for content attribute to clean and format content
     */
    public function setContentAttribute($value)
    {
        // Clean up the content before storing
        $this->attributes['content'] = $this->cleanHtmlContent($value);
    }

    /**
     * Clean HTML content by removing unnecessary wrapping and encoding
     */
    private function cleanHtmlContent($content)
    {
        if (empty($content)) {
            return $content;
        }

        // Trim whitespace
        $content = trim($content);

        // Convert line breaks to proper HTML paragraphs for better formatting
        if (!preg_match('/<[^>]+>/', $content)) {
            // If no HTML tags, convert line breaks to paragraphs
            $paragraphs = explode("\n\n", $content);
            $paragraphs = array_map('trim', $paragraphs);
            $paragraphs = array_filter($paragraphs);
            $content = '<p>' . implode('</p><p>', $paragraphs) . '</p>';
        }

        return $content;
    }
}
