<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ekadash_sadasyata', function (Blueprint $table) {
            $table->id();
            $table->string('membership_number')->unique(); // Auto-generated EKDS000001 style
            $table->string('photo');
            $table->string('signature');
            $table->string('name');
            $table->string('fathers_husband_name')->nullable();
            $table->string('mobile_number', 10)->unique();
            $table->date('birth_date');
            $table->string('marital_status')->nullable();
            $table->date('vivah_tithi')->nullable(); // Marriage date
            $table->integer('family_members')->nullable();
            $table->string('education')->nullable();
            $table->string('course_stream_name')->nullable(); // Course or stream name
            $table->string('caste_details')->nullable();
            $table->string('vibhagiy_padnaam')->nullable(); // Departmental position
            $table->string('department_name')->nullable();
            $table->string('office')->nullable();
            $table->string('karyalay_ka_pata')->nullable(); // Office address
            $table->text('vartaman_pata')->nullable(); // Current address
            $table->text('isthayi_pata')->nullable(); // Permanent address
            $table->string('member_name_signature');
            $table->string('vibhag')->nullable();
            $table->string('mobile', 10); // Proposer mobile
            $table->string('proposer_address')->nullable();
            $table->boolean('declaration')->default(false);
            $table->json('children')->nullable();
            $table->json('ancestors')->nullable();
            $table->string('rejection_reason')->nullable();

            // Foreign keys for master data
            $table->unsignedBigInteger('education_qualification_id')->nullable();
            $table->unsignedBigInteger('office_master_id')->nullable();
            $table->unsignedBigInteger('department_master_id')->nullable();
            $table->unsignedBigInteger('yadav_varg_id')->nullable();

            // Location master data foreign keys
            $table->unsignedBigInteger('division_master_id')->nullable();
            $table->unsignedBigInteger('district_master_id')->nullable();
            $table->unsignedBigInteger('vikaskhand_master_id')->nullable();

            // Status and activation
            $table->boolean('is_active')->default(true);
            $table->enum('status', ['pending', 'approved', 'rejected'])->default('pending');

            // Fixed fee for Ekadash Sadasyata
            $table->decimal('fee', 8, 2)->default(11.00); // Minimum 11 rupees

            $table->timestamps();

            // Foreign key constraints
            $table->foreign('education_qualification_id')->references('id')->on('education_qualifications')->onDelete('set null');
            $table->foreign('office_master_id')->references('id')->on('office_masters')->onDelete('set null');
            $table->foreign('department_master_id')->references('id')->on('department_masters')->onDelete('set null');
            $table->foreign('yadav_varg_id')->references('id')->on('yadav_vargs')->onDelete('set null');
            $table->foreign('division_master_id')->references('id')->on('division_masters')->onDelete('set null');
            $table->foreign('district_master_id')->references('id')->on('district_masters')->onDelete('set null');
            $table->foreign('vikaskhand_master_id')->references('id')->on('vikaskhand_masters')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ekadash_sadasyata');
    }
};
