<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified" elementFormDefault="qualified" targetNamespace="https://phar.io/xml/manifest/1.0"
           xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:ns="https://phar.io/xml/manifest/1.0">

  <xs:element name="phar">
    <xs:complexType>
      <xs:sequence>
        <xs:element ref="ns:contains" maxOccurs="1" />
        <xs:element ref="ns:copyright" maxOccurs="1" />
        <xs:element ref="ns:requires" maxOccurs="1" />
        <xs:element ref="ns:bundles" minOccurs="0" maxOccurs="1" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>

  <xs:element name="contains">
    <xs:complexType>
      <xs:simpleContent>
        <xs:extension base="xs:string">
          <xs:attribute type="xs:string" use="required" name="name"/>
          <xs:attribute type="xs:string" use="required" name="version"/>
          <xs:attribute use="required" name="type">
            <xs:simpleType>
              <xs:restriction base="xs:string">
                <xs:enumeration value="application"/>
                <xs:enumeration value="extension"/>
                <xs:enumeration value="library"/>
                <xs:enumeration value="stub"/>
              </xs:restriction>
            </xs:simpleType>
          </xs:attribute>
        </xs:extension>
      </xs:simpleContent>
    </xs:complexType>
  </xs:element>

  <xs:element name="copyright">
    <xs:complexType>
      <xs:sequence>
        <xs:choice maxOccurs="unbounded">
          <xs:element ref="ns:author" minOccurs="1" maxOccurs="unbounded" />
        </xs:choice>
        <xs:element ref="ns:license" minOccurs="1" maxOccurs="1" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>

  <xs:element name="author">
    <xs:complexType>
      <xs:simpleContent>
        <xs:extension base="xs:string">
          <xs:attribute type="xs:string" use="required" name="name"/>
          <xs:attribute type="xs:string" use="optional" name="email"/>
        </xs:extension>
      </xs:simpleContent>
    </xs:complexType>
  </xs:element>

  <xs:element name="license">
    <xs:complexType>
      <xs:simpleContent>
        <xs:extension base="xs:string">
          <xs:attribute type="xs:string" use="required" name="type"/>
          <xs:attribute type="xs:string" use="required" name="url"/>
        </xs:extension>
      </xs:simpleContent>
    </xs:complexType>
  </xs:element>

  <xs:element name="requires">
    <xs:complexType>
      <xs:sequence>
        <xs:element ref="ns:php" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>

  <xs:element name="php">
    <xs:complexType>
      <xs:sequence>
        <xs:element ref="ns:ext" maxOccurs="unbounded" minOccurs="0"  />
      </xs:sequence>
      <xs:attribute type="xs:string" use="required" name="version"/>
    </xs:complexType>
  </xs:element>

  <xs:element name="ext">
    <xs:complexType>
      <xs:simpleContent>
        <xs:extension base="xs:string">
          <xs:attribute type="xs:string" name="name" use="required" />
        </xs:extension>
      </xs:simpleContent>
    </xs:complexType>
  </xs:element>

  <xs:element name="bundles">
    <xs:complexType>
      <xs:sequence>
        <xs:element ref="ns:component" maxOccurs="unbounded" minOccurs="0" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>

  <xs:element name="component">
    <xs:complexType>
      <xs:simpleContent>
        <xs:extension base="xs:string">
          <xs:attribute type="xs:string" name="name" use="required"/>
          <xs:attribute type="xs:string" name="version" use="required"/>
        </xs:extension>
      </xs:simpleContent>
    </xs:complexType>
  </xs:element>

</xs:schema>
