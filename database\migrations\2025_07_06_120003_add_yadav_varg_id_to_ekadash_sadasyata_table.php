<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ekadash_sadasyata', function (Blueprint $table) {
            // Add yadav_varg_id column
            $table->unsignedBigInteger('yadav_varg_id')->nullable()->after('department_master_id');
            
            // Add foreign key constraint
            $table->foreign('yadav_varg_id')->references('id')->on('yadav_vargs')->onDelete('set null');
            
            // Add index for better performance
            $table->index('yadav_varg_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ekadash_sadasyata', function (Blueprint $table) {
            // Drop foreign key constraint first
            $table->dropForeign(['yadav_varg_id']);
            
            // Drop the column
            $table->dropColumn('yadav_varg_id');
        });
    }
};
