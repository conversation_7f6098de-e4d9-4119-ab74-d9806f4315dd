<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('vaivahik_panjiyans', function (Blueprint $table) {
            // Add foreign key columns
            $table->unsignedBigInteger('education_qualification_id')->nullable()->after('saikshanik_yogyata');
            $table->unsignedBigInteger('office_master_id')->nullable()->after('vartaman_karya');

            // Add foreign key constraints
            $table->foreign('education_qualification_id')->references('id')->on('education_qualifications')->onDelete('set null');
            $table->foreign('office_master_id')->references('id')->on('office_masters')->onDelete('set null');

            // Add indexes for better performance
            $table->index('education_qualification_id');
            $table->index('office_master_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('vaivahik_panjiyans', function (Blueprint $table) {
            // Drop foreign key constraints first
            $table->dropForeign(['education_qualification_id']);
            $table->dropForeign(['office_master_id']);

            // Drop the columns
            $table->dropColumn(['education_qualification_id', 'office_master_id']);
        });
    }
};
