<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\DivisionMaster;

class DivisionMasterSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $divisions = [
            [
                'division_code' => 'LKO',
                'division_name_eng' => 'Lucknow',
                'division_name_hin' => 'लखनऊ',
                'is_active' => true,
                'display_order' => 1,
            ],
            [
                'division_code' => 'KNP',
                'division_name_eng' => 'Kanpur',
                'division_name_hin' => 'कानपुर',
                'is_active' => true,
                'display_order' => 2,
            ],
            [
                'division_code' => 'AGR',
                'division_name_eng' => 'Agra',
                'division_name_hin' => 'आगरा',
                'is_active' => true,
                'display_order' => 3,
            ],
            [
                'division_code' => 'MRT',
                'division_name_eng' => 'Meerut',
                'division_name_hin' => 'मेरठ',
                'is_active' => true,
                'display_order' => 4,
            ],
            [
                'division_code' => 'VNS',
                'division_name_eng' => 'Varanasi',
                'division_name_hin' => 'वाराणसी',
                'is_active' => true,
                'display_order' => 5,
            ],
            [
                'division_code' => 'GRK',
                'division_name_eng' => 'Gorakhpur',
                'division_name_hin' => 'गोरखपुर',
                'is_active' => true,
                'display_order' => 6,
            ],
            [
                'division_code' => 'ALH',
                'division_name_eng' => 'Allahabad',
                'division_name_hin' => 'इलाहाबाद',
                'is_active' => true,
                'display_order' => 7,
            ],
            [
                'division_code' => 'JHS',
                'division_name_eng' => 'Jhansi',
                'division_name_hin' => 'झांसी',
                'is_active' => true,
                'display_order' => 8,
            ],
            [
                'division_code' => 'CHT',
                'division_name_eng' => 'Chitrakoot',
                'division_name_hin' => 'चित्रकूट',
                'is_active' => true,
                'display_order' => 9,
            ],
            [
                'division_code' => 'DEV',
                'division_name_eng' => 'Devipatan',
                'division_name_hin' => 'देवीपाटन',
                'is_active' => true,
                'display_order' => 10,
            ],
            [
                'division_code' => 'FZB',
                'division_name_eng' => 'Faizabad',
                'division_name_hin' => 'फैजाबाद',
                'is_active' => true,
                'display_order' => 11,
            ],
            [
                'division_code' => 'MRD',
                'division_name_eng' => 'Moradabad',
                'division_name_hin' => 'मुरादाबाद',
                'is_active' => true,
                'display_order' => 12,
            ],
            [
                'division_code' => 'SHJ',
                'division_name_eng' => 'Shahjahanpur',
                'division_name_hin' => 'शाहजहांपुर',
                'is_active' => true,
                'display_order' => 13,
            ],
            [
                'division_code' => 'BRD',
                'division_name_eng' => 'Bareilly',
                'division_name_hin' => 'बरेली',
                'is_active' => true,
                'display_order' => 14,
            ],
            [
                'division_code' => 'ALG',
                'division_name_eng' => 'Aligarh',
                'division_name_hin' => 'अलीगढ़',
                'is_active' => true,
                'display_order' => 15,
            ],
            [
                'division_code' => 'SHR',
                'division_name_eng' => 'Saharanpur',
                'division_name_hin' => 'सहारनपुर',
                'is_active' => true,
                'display_order' => 16,
            ],
            [
                'division_code' => 'AZG',
                'division_name_eng' => 'Azamgarh',
                'division_name_hin' => 'आजमगढ़',
                'is_active' => true,
                'display_order' => 17,
            ],
            [
                'division_code' => 'MRZ',
                'division_name_eng' => 'Mirzapur',
                'division_name_hin' => 'मिर्जापुर',
                'is_active' => true,
                'display_order' => 18,
            ],
        ];

        foreach ($divisions as $division) {
            DivisionMaster::updateOrCreate(
                ['division_code' => $division['division_code']],
                $division
            );
        }
    }
}
