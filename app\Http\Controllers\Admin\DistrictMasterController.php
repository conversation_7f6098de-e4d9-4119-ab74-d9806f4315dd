<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\DistrictMaster;
use App\Models\DivisionMaster;
use Illuminate\Http\Request;

class DistrictMasterController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = DistrictMaster::with('division');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('district_name_eng', 'like', "%{$search}%")
                  ->orWhere('district_name_hin', 'like', "%{$search}%")
                  ->orWhere('district_lgd_code', 'like', "%{$search}%")
                  ->orWhere('district_short_name', 'like', "%{$search}%");
            });
        }

        // Status filter
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->active();
            } elseif ($request->status === 'inactive') {
                $query->inactive();
            }
        }

        // Division filter
        if ($request->filled('division_code')) {
            $query->where('division_code', $request->division_code);
        }

        $districts = $query->ordered()->paginate(15);
        $divisions = DivisionMaster::active()->ordered()->get();

        return view('admin.district-masters.index', compact('districts', 'divisions'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $divisions = DivisionMaster::active()->ordered()->get();
        return view('admin.district-masters.create', compact('divisions'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'district_lgd_code' => 'required|string|max:255|unique:district_masters,district_lgd_code',
                'district_name_eng' => 'required|string|max:255',
                'district_name_hin' => 'required|string|max:255',
                'district_short_name' => 'nullable|string|max:255',
                'division_code' => 'required|string|exists:division_masters,division_code',
                'is_active' => 'boolean',
                'display_order' => 'nullable|integer|min:0',
            ]);

            $validated['is_active'] = $request->has('is_active');
            $validated['display_order'] = $validated['display_order'] ?? 0;

            DistrictMaster::create($validated);

            return redirect()->route('admin.district-masters.index')
                           ->with('success', 'जिला सफलतापूर्वक जोड़ा गया।');
        } catch (\Exception $e) {
            return redirect()->back()
                           ->withInput()
                           ->with('error', 'जिला जोड़ने में त्रुटि: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(DistrictMaster $districtMaster)
    {
        $districtMaster->load('division');
        return view('admin.district-masters.show', compact('districtMaster'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(DistrictMaster $districtMaster)
    {
        $divisions = DivisionMaster::active()->ordered()->get();
        return view('admin.district-masters.edit', compact('districtMaster', 'divisions'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, DistrictMaster $districtMaster)
    {
        try {
            $validated = $request->validate([
                'district_lgd_code' => 'required|string|max:255|unique:district_masters,district_lgd_code,' . $districtMaster->id,
                'district_name_eng' => 'required|string|max:255',
                'district_name_hin' => 'required|string|max:255',
                'district_short_name' => 'nullable|string|max:255',
                'division_code' => 'required|string|exists:division_masters,division_code',
                'is_active' => 'boolean',
                'display_order' => 'nullable|integer|min:0',
            ]);

            $validated['is_active'] = $request->has('is_active');
            $validated['display_order'] = $validated['display_order'] ?? 0;

            $districtMaster->update($validated);

            return redirect()->route('admin.district-masters.index')
                           ->with('success', 'जिला सफलतापूर्वक अपडेट किया गया।');
        } catch (\Exception $e) {
            return redirect()->back()
                           ->withInput()
                           ->with('error', 'जिला अपडेट करने में त्रुटि: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(DistrictMaster $districtMaster)
    {
        try {
            $districtMaster->delete();
            return redirect()->route('admin.district-masters.index')
                           ->with('success', 'जिला सफलतापूर्वक हटाया गया।');
        } catch (\Exception $e) {
            return redirect()->back()
                           ->with('error', 'जिला हटाने में त्रुटि: ' . $e->getMessage());
        }
    }
}
