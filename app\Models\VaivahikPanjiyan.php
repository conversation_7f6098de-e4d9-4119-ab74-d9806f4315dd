<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class VaivahikPanjiyan extends Authenticatable
{
    use HasFactory, Notifiable;

    protected $fillable = [
        'naam',
        'ling',
        'mobile',
        'owner_email',
        'owner_password',
        'email_verified_at',
        'is_active',
        'status',
        'janmatithi',
        'janmasamay_din',
        'pita_ka_naam',
        'mata_ka_naam',
        'bhai_bahan_vivran',
        'pata',
        'jati',
        'upjati',
        'gotra',
        'uchai',
        'saikshanik_yogyata',
        'vartaman_karya',
        'upjivika',
        'family_member_membership_number',
        'biodata',
        'photo1',
        'photo2',
        'photo3',
        'photo4',
        'education_qualification_id',
        'office_master_id',
        'department_master_id',
        'yadav_varg_id',
    ];

    protected $hidden = [
        'owner_password',
        'remember_token',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'is_active' => 'boolean',
        'janmatithi' => 'date',
    ];

    // Status constants
    public const STATUS_PENDING = 'pending';
    public const STATUS_APPROVED = 'approved';
    public const STATUS_REJECTED = 'rejected';

    /**
     * Get the name of the unique identifier for the user.
     */
    public function getAuthIdentifierName()
    {
        return 'id';
    }

    /**
     * Get the unique identifier for the user.
     */
    public function getAuthIdentifier()
    {
        return $this->getKey();
    }

    /**
     * Get the password for the user.
     */
    public function getAuthPassword()
    {
        return $this->owner_password;
    }

    /**
     * Get the token value for the "remember me" session.
     */
    public function getRememberToken()
    {
        return $this->remember_token;
    }

    /**
     * Set the token value for the "remember me" session.
     */
    public function setRememberToken($value)
    {
        $this->remember_token = $value;
    }

    /**
     * Get the column name for the "remember me" token.
     */
    public function getRememberTokenName()
    {
        return 'remember_token';
    }

    /**
     * Check if profile is approved
     */
    public function isApproved()
    {
        return $this->status === self::STATUS_APPROVED;
    }

    /**
     * Check if profile is pending
     */
    public function isPending()
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Check if profile is rejected
     */
    public function isRejected()
    {
        return $this->status === self::STATUS_REJECTED;
    }

    /**
     * Get interest requests received for this profile
     */
    public function receivedInterests()
    {
        return $this->hasMany(InterestRequest::class, 'profile_id');
    }

    /**
     * Get interest requests sent by this profile owner
     */
    public function sentInterests()
    {
        return $this->hasMany(InterestRequest::class, 'requester_id');
    }

    /**
     * Get formatted birth date
     */
    public function getFormattedBirthDateAttribute()
    {
        if (!$this->janmatithi) {
            return null;
        }

        try {
            $dateString = trim($this->janmatithi);

            if (preg_match('/^\d{4}-\d{2}-\d{2}$/', $dateString)) {
                return \Carbon\Carbon::createFromFormat('Y-m-d', $dateString);
            } elseif (preg_match('/^\d{2}\/\d{2}\/\d{4}$/', $dateString)) {
                return \Carbon\Carbon::createFromFormat('d/m/Y', $dateString);
            } elseif (preg_match('/^\d{2}-\d{2}-\d{4}$/', $dateString)) {
                return \Carbon\Carbon::createFromFormat('d-m-Y', $dateString);
            } else {
                return \Carbon\Carbon::parse($dateString);
            }
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Get age from birth date
     */
    public function getAgeAttribute()
    {
        $birthDate = $this->getFormattedBirthDateAttribute();
        return $birthDate ? $birthDate->age : null;
    }

    /**
     * Get formatted birth date string
     */
    public function getBirthDateStringAttribute()
    {
        $birthDate = $this->getFormattedBirthDateAttribute();
        return $birthDate ? $birthDate->format('d/m/Y') : $this->janmatithi;
    }

    /**
     * Get the education qualification for this vaivahik panjiyan.
     */
    public function educationQualification()
    {
        return $this->belongsTo(EducationQualification::class, 'education_qualification_id');
    }

    /**
     * Get the office master for this vaivahik panjiyan.
     */
    public function officeMaster()
    {
        return $this->belongsTo(OfficeMaster::class, 'office_master_id');
    }

    /**
     * Get the department master for this vaivahik panjiyan.
     */
    public function departmentMaster()
    {
        return $this->belongsTo(DepartmentMaster::class, 'department_master_id');
    }

    /**
     * Get the yadav varg for this vaivahik panjiyan.
     */
    public function yadavVarg()
    {
        return $this->belongsTo(YadavVarg::class, 'yadav_varg_id');
    }
}
