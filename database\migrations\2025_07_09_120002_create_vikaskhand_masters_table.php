<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vikaskhand_masters', function (Blueprint $table) {
            $table->id();
            $table->string('sub_district_lgd_code')->unique(); // Unique LGD code for sub-district
            $table->string('sub_district_name_eng'); // Sub-district name in English
            $table->string('sub_district_name_hin')->nullable(); // Sub-district name in Hindi
            $table->string('district_lgd_code'); // Foreign key to district_masters
            $table->boolean('is_active')->default(true); // Active/Inactive status
            $table->integer('display_order')->default(0); // For ordering in dropdowns
            $table->timestamps();

            // Add foreign key constraint
            $table->foreign('district_lgd_code')->references('district_lgd_code')->on('district_masters')->onDelete('cascade');

            // Add indexes for better performance
            $table->index('sub_district_lgd_code');
            $table->index('district_lgd_code');
            $table->index('is_active');
            $table->index('display_order');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vikaskhand_masters');
    }
};
