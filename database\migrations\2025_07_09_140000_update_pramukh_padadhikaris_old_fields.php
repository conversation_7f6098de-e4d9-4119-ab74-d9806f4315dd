<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pramukh_padadhikaris', function (Blueprint $table) {
            // Make old fields nullable so they don't conflict with new fields
            $table->string('name')->nullable()->change();
            $table->string('post_in_community')->nullable()->change();
            $table->text('address')->nullable()->change();
            $table->string('office_name')->nullable()->change();
            $table->string('office_post')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pramukh_padadhikaris', function (Blueprint $table) {
            // Revert old fields back to required (if needed)
            $table->string('name')->nullable(false)->change();
            $table->string('post_in_community')->nullable(false)->change();
            $table->text('address')->nullable(false)->change();
            $table->string('office_name')->nullable()->change(); // Keep nullable as it was optional
            $table->string('office_post')->nullable()->change(); // Keep nullable as it was optional
        });
    }
};
