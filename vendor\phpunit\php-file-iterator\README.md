[![Latest Stable Version](https://poser.pugx.org/phpunit/php-file-iterator/v/stable.png)](https://packagist.org/packages/phpunit/php-file-iterator)
[![CI Status](https://github.com/sebastian<PERSON>mann/php-file-iterator/workflows/CI/badge.svg)](https://github.com/sebastian<PERSON>mann/php-file-iterator/actions)
[![Type Coverage](https://shepherd.dev/github/sebastian<PERSON>mann/php-file-iterator/coverage.svg)](https://shepherd.dev/github/sebastian<PERSON>mann/php-file-iterator)
[![codecov](https://codecov.io/gh/sebastian<PERSON>mann/php-file-iterator/branch/main/graph/badge.svg)](https://codecov.io/gh/sebastian<PERSON>mann/php-file-iterator)

# php-file-iterator

## Installation

You can add this library as a local, per-project dependency to your project using [Composer](https://getcomposer.org/):

    composer require phpunit/php-file-iterator

If you only need this library during development, for instance to run your project's test suite, then you should add it as a development-time dependency:

    composer require --dev phpunit/php-file-iterator

