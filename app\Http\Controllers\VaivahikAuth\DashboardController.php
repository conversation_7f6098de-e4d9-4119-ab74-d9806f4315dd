<?php

namespace App\Http\Controllers\VaivahikAuth;

use App\Http\Controllers\Controller;
use App\Models\InterestRequest;
use App\Models\VaivahikPanjiyan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;

class DashboardController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth:vaivahik');
    }

    /**
     * Show the vaivahik user dashboard.
     */
    public function index()
    {
        $user = Auth::guard('vaivahik')->user();
        
        // Get statistics
        $stats = [
            'received_requests' => InterestRequest::where('profile_id', $user->id)->count(),
            'pending_requests' => InterestRequest::where('profile_id', $user->id)
                ->where('status', InterestRequest::STATUS_PENDING)->count(),
            'accepted_requests' => InterestRequest::where('profile_id', $user->id)
                ->where('status', InterestRequest::STATUS_ACCEPTED)->count(),
            'sent_requests' => InterestRequest::where('requester_id', $user->id)
                ->where('requester_type', 'vaivahik')->count(),
        ];
        
        return view('vaivahik-auth.dashboard', compact('user', 'stats'));
    }

    /**
     * Show received interest requests
     */
    public function receivedRequests()
    {
        $user = Auth::guard('vaivahik')->user();
        
        $requests = InterestRequest::where('profile_id', $user->id)
            ->with(['vaivahikRequester', 'memberRequester'])
            ->latest()
            ->paginate(10);

        return view('vaivahik-auth.received-requests', compact('requests'));
    }

    /**
     * Show sent interest requests
     */
    public function sentRequests()
    {
        $user = Auth::guard('vaivahik')->user();
        
        $requests = InterestRequest::where('requester_id', $user->id)
            ->where('requester_type', 'vaivahik')
            ->with('profile')
            ->latest()
            ->paginate(10);

        return view('vaivahik-auth.sent-requests', compact('requests'));
    }

    /**
     * Accept an interest request
     */
    public function acceptRequest($requestId)
    {
        $user = Auth::guard('vaivahik')->user();
        
        $request = InterestRequest::where('id', $requestId)
            ->where('profile_id', $user->id)
            ->firstOrFail();

        $request->accept();

        return response()->json([
            'success' => true,
            'message' => 'रुचि अनुरोध स्वीकार किया गया।'
        ]);
    }

    /**
     * Reject an interest request
     */
    public function rejectRequest($requestId)
    {
        $user = Auth::guard('vaivahik')->user();

        $request = InterestRequest::where('id', $requestId)
            ->where('profile_id', $user->id)
            ->firstOrFail();

        $request->reject();

        return response()->json([
            'success' => true,
            'message' => 'रुचि अनुरोध अस्वीकार किया गया।'
        ]);
    }

    /**
     * Respond to an interest request (accept or reject)
     */
    public function respondToRequest(Request $request, $requestId)
    {
        $user = Auth::guard('vaivahik')->user();

        $interestRequest = InterestRequest::where('id', $requestId)
            ->where('profile_id', $user->id)
            ->firstOrFail();

        $action = $request->input('action');

        if ($action === 'accept') {
            $interestRequest->update([
                'status' => InterestRequest::STATUS_ACCEPTED,
                'accepted_at' => now(),
                'responded_at' => now(),
            ]);
            $message = 'रुचि अनुरोध स्वीकार किया गया।';
        } elseif ($action === 'reject') {
            $interestRequest->update([
                'status' => InterestRequest::STATUS_REJECTED,
                'rejected_at' => now(),
                'responded_at' => now(),
            ]);
            $message = 'रुचि अनुरोध अस्वीकार किया गया।';
        } else {
            return back()->with('error', 'अमान्य कार्य।');
        }

        return back()->with('success', $message);
    }

    /**
     * Show profile edit form
     */
    public function editProfile()
    {
        $user = Auth::guard('vaivahik')->user();
        return view('vaivahik-auth.edit-profile', compact('user'));
    }

    /**
     * Update profile
     */
    public function updateProfile(Request $request)
    {
        $user = Auth::guard('vaivahik')->user();

        $request->validate([
            'naam' => 'required|string|max:255',
            'ling' => 'nullable|string|in:पुरुष,महिला,अन्य',
            'mobile' => 'nullable|string|size:10',
            'owner_email' => 'required|email|unique:vaivahik_panjiyans,owner_email,' . $user->id,
            'janmatithi' => 'nullable|date',
            'janmasamay_din' => 'nullable|string',
            'pita_ka_naam' => 'nullable|string',
            'mata_ka_naam' => 'nullable|string',
            'bhai_bahan_vivran' => 'nullable|string',
            'pata' => 'nullable|string',
            'jati' => 'nullable|string',
            'upjati' => 'nullable|string',
            'gotra' => 'nullable|string',
            'uchai' => 'nullable|integer|min:100|max:250',
            'saikshanik_yogyata' => 'nullable|string',
            'upjivika' => 'required|string|max:255',
            'vartaman_karya' => 'nullable|string',
            'family_member_membership_number' => 'required|string|max:255',
            'photo1' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
            'photo2' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
            'photo3' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
            'photo4' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
            'biodata' => 'nullable|file|mimes:pdf|max:5120',
            'current_password' => 'nullable|string',
            'password' => 'nullable|string|min:8|confirmed',
        ]);

        // Verify family member membership number (required)
        $membershipExists = \App\Models\Membership::where('membership_number', $request->family_member_membership_number)
            ->where('status', \App\Models\Membership::STATUS_APPROVED)
            ->where('is_active', true)
            ->exists();

        if (!$membershipExists) {
            return back()->withErrors([
                'family_member_membership_number' => 'यह सदस्यता संख्या मान्य नहीं है या स्वीकृत नहीं है। कृपया सही सदस्यता संख्या दर्ज करें।'
            ])->withInput();
        }

        $updateData = $request->except(['current_password', 'password', 'password_confirmation', 'photo1', 'photo2', 'photo3', 'photo4', 'biodata']);

        // Handle photo uploads
        for ($i = 1; $i <= 4; $i++) {
            $photoField = 'photo' . $i;
            if ($request->hasFile($photoField)) {
                // Delete old photo if exists
                if ($user->$photoField) {
                    Storage::disk('public')->delete($user->$photoField);
                }
                // Store new photo
                $updateData[$photoField] = $request->file($photoField)->store('photos', 'public');
            }
        }

        // Handle biodata upload
        if ($request->hasFile('biodata')) {
            // Delete old biodata if exists
            if ($user->biodata) {
                Storage::disk('public')->delete($user->biodata);
            }
            // Store new biodata
            $updateData['biodata'] = $request->file('biodata')->store('biodata', 'public');
        }

        // Handle password update
        if ($request->filled('password')) {
            if (!$request->filled('current_password') ||
                !Hash::check($request->current_password, $user->owner_password)) {
                return back()->withErrors(['current_password' => 'वर्तमान पासवर्ड गलत है।']);
            }
            $updateData['owner_password'] = Hash::make($request->password);
        }

        $user->update($updateData);

        return back()->with('success', 'प्रोफाइल सफलतापूर्वक अपडेट की गई।');
    }

    /**
     * Show contact information for accepted requests
     */
    public function showContactInfo($requestId)
    {
        $user = Auth::guard('vaivahik')->user();
        
        $request = InterestRequest::where('id', $requestId)
            ->where('requester_id', $user->id)
            ->where('requester_type', 'vaivahik')
            ->where('status', InterestRequest::STATUS_ACCEPTED)
            ->with('profile')
            ->firstOrFail();

        return response()->json([
            'success' => true,
            'contact_info' => [
                'mobile' => $request->profile->mobile,
                'email' => $request->profile->owner_email,
                'name' => $request->profile->naam,
            ]
        ]);
    }
}
