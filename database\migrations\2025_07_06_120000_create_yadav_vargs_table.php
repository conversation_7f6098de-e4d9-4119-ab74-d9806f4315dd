<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('yadav_vargs', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique(); // e.g., 'अहीर', 'गोप', 'यादव', 'गोवाल'
            $table->string('name_english')->nullable(); // English name for reference
            $table->text('description')->nullable(); // Description of yadav varg
            $table->string('category')->nullable(); // e.g., 'traditional', 'regional', 'historical'
            $table->boolean('is_active')->default(true); // Active/Inactive status
            $table->integer('display_order')->default(0); // For ordering in dropdowns
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('yadav_vargs');
    }
};
