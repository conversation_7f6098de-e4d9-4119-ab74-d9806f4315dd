<?php

namespace App\Http\Controllers;

use App\Models\AboutContent;
use App\Models\OrganizationDocument;
use App\Models\PramukhPadadhikari;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class HumareBareController extends Controller
{
    /**
     * Display the uddeshya-aur-itihas (purpose and history) page.
     */
    public function uddeshyaAurItihas()
    {
        $contents = AboutContent::getContentByPage(AboutContent::PAGE_UDDESHYA);
        return view('humare-bare-me.uddeshya-aur-itihas', compact('contents'));
    }
    
    /**
     * DDisplay the pramukh-padadhikari (key officials) page.
     */
    public function pramukhPadadhikari()
    {
        $contents = AboutContent::getContentByPage(AboutContent::PAGE_PRAMUKH);
        $pramukhPadadhikari = PramukhPadadhikari::get();
        return view('humare-bare-me.pramukh-padadhikari', compact('contents', 'pramukhPadadhikari'));
    }
    
    /**
     * Display the sanrachna-karyapranali (structure/organization) page.
     */
    public function sanrachnaKaryapranali()
    {
        $contents = AboutContent::getContentByPage(AboutContent::PAGE_SANRACHNA);
        return view('humare-bare-me.sanrachna', compact('contents'));
    }

    /**
     * Display the organization documents page.
     */
    public function sanghatanDastavej()
    {
        $documents = OrganizationDocument::getPublishedDocuments();
        return view('humare-bare-me.sanghatan-dastavej', compact('documents'));
    }

    /**
     * Download a document and increment download count.
     */
    public function downloadDocument($id)
    {
        $document = OrganizationDocument::where('id', $id)
            ->where('is_published', true)
            ->firstOrFail();

        if (!$document->fileExists()) {
            abort(404, 'File not found');
        }

        // Increment download count
        $document->incrementDownloadCount();

        // Get file path and return download response
        $filePath = storage_path('app/' . $document->file_path);

        return response()->download($filePath, $document->file_name);
    }
}
