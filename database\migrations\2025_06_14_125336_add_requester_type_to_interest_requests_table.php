<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('interest_requests', function (Blueprint $table) {
            // Check if column doesn't exist before adding
            if (!Schema::hasColumn('interest_requests', 'requester_type')) {
                $table->string('requester_type')->default('vaivahik')->after('requester_id');
            }

            // Add the unique constraint that was missing (only if it doesn't exist)
            $indexExists = collect(DB::select("SHOW INDEX FROM interest_requests WHERE Key_name = 'unique_interest_request'"))->isNotEmpty();
            if (!$indexExists) {
                $table->unique(['requester_id', 'requester_type', 'profile_id'], 'unique_interest_request');
            }

            // Add indexes for better performance (only if they don't exist)
            $requesterIndexExists = collect(DB::select("SHOW INDEX FROM interest_requests WHERE Key_name = 'idx_requester'"))->isNotEmpty();
            if (!$requesterIndexExists) {
                $table->index(['requester_id', 'requester_type'], 'idx_requester');
            }

            $profileStatusIndexExists = collect(DB::select("SHOW INDEX FROM interest_requests WHERE Key_name = 'idx_profile_status'"))->isNotEmpty();
            if (!$profileStatusIndexExists) {
                $table->index(['profile_id', 'status'], 'idx_profile_status');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('interest_requests', function (Blueprint $table) {
            $table->dropIndex('unique_interest_request');
            $table->dropIndex('idx_requester');
            $table->dropIndex('idx_profile_status');
            $table->dropColumn('requester_type');
        });
    }
};
