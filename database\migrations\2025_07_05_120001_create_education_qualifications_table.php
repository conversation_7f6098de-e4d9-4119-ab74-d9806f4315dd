<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('education_qualifications', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique(); // e.g., 'स्नातक', 'स्नातकोत्तर', 'डॉक्टरेट', 'डिप्लोमा'
            $table->string('name_english')->nullable(); // English name for reference
            $table->text('description')->nullable(); // Description of qualification
            $table->string('level')->nullable(); // e.g., 'undergraduate', 'postgraduate', 'doctorate'
            $table->boolean('is_active')->default(true); // Active/Inactive status
            $table->integer('display_order')->default(0); // For ordering in dropdowns
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('education_qualifications');
    }
};
