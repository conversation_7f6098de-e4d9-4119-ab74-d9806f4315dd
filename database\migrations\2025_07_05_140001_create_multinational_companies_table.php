<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('multinational_companies', function (Blueprint $table) {
            $table->id();
            $table->string('naam');
            $table->string('pita_ka_naam');
            $table->text('pata');
            $table->integer('umra');
            $table->text('ahartayen');
            $table->string('company_name');
            $table->string('location');
            $table->string('mobile_number');
            $table->string('package');
            $table->string('biodata')->nullable(); // File path for uploaded biodata
            $table->string('media_link')->nullable(); // 3 minutes video link
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('multinational_companies');
    }
};
