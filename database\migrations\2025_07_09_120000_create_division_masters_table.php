<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('division_masters', function (Blueprint $table) {
            $table->id();
            $table->string('division_code')->unique(); // Unique division code
            $table->string('division_name_eng'); // Division name in English
            $table->string('division_name_hin')->nullable(); // Division name in Hindi
            $table->boolean('is_active')->default(true); // Active/Inactive status
            $table->integer('display_order')->default(0); // For ordering in dropdowns
            $table->timestamps();

            // Add indexes for better performance
            $table->index('division_code');
            $table->index('is_active');
            $table->index('display_order');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('division_masters');
    }
};
