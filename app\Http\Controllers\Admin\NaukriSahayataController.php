<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\NaukriSahayta;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class NaukriSahayataController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('admin.auth');
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = NaukriSahayta::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('naam', 'like', "%{$search}%")
                  ->orWhere('pita_ka_naam', 'like', "%{$search}%")
                  ->orWhere('mobile_number', 'like', "%{$search}%")
                  ->orWhere('ahartayen', 'like', "%{$search}%")
                  ->orWhere('ruchi', 'like', "%{$search}%");
            });
        }

        $naukriSahayta = $query->orderBy('created_at', 'desc')->paginate(15);

        return view('admin.naukri-sahayta.index', compact('naukriSahayta'));
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $naukriSahayta = NaukriSahayta::findOrFail($id);
        return view('admin.naukri-sahayta.show', compact('naukriSahayta'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $naukriSahayta = NaukriSahayta::findOrFail($id);
        return view('admin.naukri-sahayta.edit', compact('naukriSahayta'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            $naukriSahayta = NaukriSahayta::findOrFail($id);

            $validated = $request->validate([
                'naam' => 'required|string|max:255',
                'pita_ka_naam' => 'required|string|max:255',
                'pata' => 'required|string|max:1000',
                'umra' => 'required|integer|min:18|max:65',
                'ahartayen' => 'required|string|max:1000',
                'ruchi' => 'required|string|max:1000',
                'mobile_number' => 'required|string|size:10|regex:/^[6-9][0-9]{9}$/',
                'biodata' => 'nullable|file|mimes:pdf,doc,docx|max:5120', // 5MB max
            ]);

            $data = $validated;

            // Handle biodata upload if new file is provided
            if ($request->hasFile('biodata') && $request->file('biodata')->isValid()) {
                // Delete old file
                $naukriSahayta->deleteBiodataFile();

                // Store new file
                $data['biodata'] = $request->file('biodata')->store('naukri-sahayta/biodata', 'public');
            }

            $naukriSahayta->update($data);

            return redirect()
                ->route('admin.naukri-sahayta.index')
                ->with('success', 'नौकरी सहायता रिकॉर्ड सफलतापूर्वक अपडेट हो गया।');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->withErrors(['error' => 'रिकॉर्ड अपडेट करने में त्रुटि: ' . $e->getMessage()]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $naukriSahayta = NaukriSahayta::findOrFail($id);
            $naukriSahayta->delete(); // This will also delete the biodata file due to model boot method

            return redirect()
                ->route('admin.naukri-sahayta.index')
                ->with('success', 'नौकरी सहायता रिकॉर्ड सफलतापूर्वक डिलीट हो गया।');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['error' => 'रिकॉर्ड डिलीट करने में त्रुटि: ' . $e->getMessage()]);
        }
    }

    /**
     * Download biodata file
     */
    public function downloadBiodata(string $id)
    {
        try {
            $naukriSahayta = NaukriSahayta::findOrFail($id);
            
            if (!$naukriSahayta->biodata || !Storage::exists($naukriSahayta->biodata)) {
                return redirect()->back()
                    ->withErrors(['error' => 'बायोडाटा फाइल उपलब्ध नहीं है।']);
            }

            return Storage::download($naukriSahayta->biodata, $naukriSahayta->naam . '_biodata.' . pathinfo($naukriSahayta->biodata, PATHINFO_EXTENSION));

        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['error' => 'फाइल डाउनलोड करने में त्रुटि: ' . $e->getMessage()]);
        }
    }
}
