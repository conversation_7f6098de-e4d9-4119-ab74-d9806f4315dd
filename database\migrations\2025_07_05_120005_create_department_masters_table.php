<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('department_masters', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique(); // e.g., 'शिक्षा विभाग', 'स्वास्थ्य विभाग', 'पुलिस विभाग'
            $table->string('name_english')->nullable(); // English name for reference
            $table->text('description')->nullable(); // Description of department
            $table->string('category')->nullable(); // e.g., 'government', 'private', 'autonomous'
            $table->string('ministry')->nullable(); // Parent ministry if applicable
            $table->boolean('is_active')->default(true); // Active/Inactive status
            $table->integer('display_order')->default(0); // For ordering in dropdowns
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('department_masters');
    }
};
