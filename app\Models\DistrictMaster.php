<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DistrictMaster extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'district_lgd_code',
        'district_name_eng',
        'district_name_hin',
        'district_short_name',
        'division_code',
        'is_active',
        'display_order',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Scope to get only active districts.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get inactive districts.
     */
    public function scopeInactive($query)
    {
        return $query->where('is_active', false);
    }

    /**
     * Scope to order by display order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('display_order')->orderBy('district_name_eng');
    }

    /**
     * Get the division that this district belongs to.
     */
    public function division()
    {
        return $this->belongsTo(DivisionMaster::class, 'division_code', 'division_code');
    }

    /**
     * Get vikaskhands that belong to this district.
     */
    public function vikaskhands()
    {
        return $this->hasMany(VikaskhandMaster::class, 'district_lgd_code', 'district_lgd_code');
    }

    /**
     * Get memberships that use this district.
     */
    public function memberships()
    {
        return $this->hasMany(Membership::class, 'district_master_id');
    }

    /**
     * Get vaivahik panjiyans that use this district.
     */
    public function vaivahikPanjiyans()
    {
        return $this->hasMany(VaivahikPanjiyan::class, 'district_master_id');
    }
}
