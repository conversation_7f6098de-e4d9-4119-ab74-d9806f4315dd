<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Activity;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class ActivitiesController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('admin.auth');
    }
    
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $category = $request->query('category', Activity::CATEGORY_SOCIAL);
        $activities = Activity::where('category', $category)
                    ->orderBy('display_order')
                    ->orderBy('event_date', 'desc')
                    ->get();
        
        return view('admin.activities.index', [
            'activities' => $activities,
            'category' => $category,
            'categoryOptions' => Activity::getCategoryOptions()
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.activities.create', [
            'categoryOptions' => Activity::getCategoryOptions()
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'category' => 'required|string',
                'title' => 'required|string|max:255',
                'description' => 'required',
                'event_date' => 'nullable|date',
                'location' => 'nullable|string|max:255',
                'image' => 'nullable|image|max:2048', // max 2MB
                'video_url' => 'nullable|url|max:255',
                'display_order' => 'nullable|integer',
            ]);

            $data = $request->except('image');
            
            // Set boolean fields explicitly
            $data['is_published'] = $request->has('is_published') ? 1 : 0;
            
            // Handle image upload if provided
            if ($request->hasFile('image') && $request->file('image')->isValid()) {
                $path = $request->file('image')->store('public/activities');
                $data['image_path'] = Storage::url($path);
            }
            
            Activity::create($data);
            
            return redirect()
                ->route('admin.activities.index', ['category' => $request->category])
                ->with('success', 'Activity created successfully');
        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->withErrors(['error' => 'Failed to create activity: ' . $e->getMessage()]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $activity = Activity::findOrFail($id);
        return view('admin.activities.show', compact('activity'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $activity = Activity::findOrFail($id);
        
        return view('admin.activities.edit', [
            'activity' => $activity,
            'categoryOptions' => Activity::getCategoryOptions()
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            $activity = Activity::findOrFail($id);
            
            $validated = $request->validate([
                'category' => 'required|string',
                'title' => 'required|string|max:255',
                'description' => 'required',
                'event_date' => 'nullable|date',
                'location' => 'nullable|string|max:255',
                'image' => 'nullable|image|max:2048', // max 2MB
                'video_url' => 'nullable|url|max:255',
                'display_order' => 'nullable|integer',
            ]);

            $data = $request->except('image');
            
            // Set boolean fields explicitly
            $data['is_published'] = $request->has('is_published') ? 1 : 0;
            
            // Handle image upload if provided
            if ($request->hasFile('image') && $request->file('image')->isValid()) {
                // Delete old image if exists
                if ($activity->image_path && Storage::exists(str_replace('/storage', 'public', $activity->image_path))) {
                    Storage::delete(str_replace('/storage', 'public', $activity->image_path));
                }
                
                $path = $request->file('image')->store('public/activities');
                $data['image_path'] = Storage::url($path);
            }
            
            $activity->update($data);
            
            return redirect()
                ->route('admin.activities.index', ['category' => $activity->category])
                ->with('success', 'Activity updated successfully');
        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->withErrors(['error' => 'Failed to update activity: ' . $e->getMessage()]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $activity = Activity::findOrFail($id);
        $category = $activity->category;
        
        // Delete associated image if exists
        if ($activity->image_path && Storage::exists(str_replace('/storage', 'public', $activity->image_path))) {
            Storage::delete(str_replace('/storage', 'public', $activity->image_path));
        }
        
        $activity->delete();
        
        return redirect()
            ->route('admin.activities.index', ['category' => $category])
            ->with('success', 'Activity deleted successfully');
    }
}
