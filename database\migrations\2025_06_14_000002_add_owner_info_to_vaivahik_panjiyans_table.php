<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('vaivahik_panjiyans', function (Blueprint $table) {
            $table->string('owner_email')->nullable()->after('mobile');
            $table->string('owner_password')->nullable()->after('owner_email');
            $table->timestamp('email_verified_at')->nullable()->after('owner_password');
            $table->boolean('is_active')->default(true)->after('email_verified_at');
            $table->enum('status', ['pending', 'approved', 'rejected'])->default('pending')->after('is_active');
            $table->rememberToken()->after('status');
            
            // Add index for faster lookups
            $table->index(['owner_email', 'status']);
            $table->index('mobile');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('vaivahik_panjiyans', function (Blueprint $table) {
            $table->dropIndex(['owner_email', 'status']);
            $table->dropIndex(['mobile']);
            $table->dropColumn([
                'owner_email',
                'owner_password', 
                'email_verified_at',
                'is_active',
                'status',
                'remember_token'
            ]);
        });
    }
};
