<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vaivahik_panjiyans', function (Blueprint $table) {
            $table->id();
            $table->string('naam');
            $table->enum('ling', ['पुरुष', 'महिला', 'अन्य']);
            $table->string('mobile');
            $table->date('janmatithi')->nullable();
            $table->string('janmasamay_din')->nullable();
            $table->string('pita_ka_naam')->nullable();
            $table->string('mata_ka_naam')->nullable();
            $table->text('bhai_bahan_vivran')->nullable();
            $table->text('pata')->nullable();
            $table->string('jati')->nullable();
            $table->string('upjati')->nullable();
            $table->string('gotra')->nullable();
            $table->string('uchai')->nullable();
            $table->string('saikshanik_yogyata')->nullable();
            $table->string('vartaman_karya')->nullable();
            $table->string('biodata')->nullable();
            $table->string('photo1')->nullable();
            $table->string('photo2')->nullable();
            $table->string('photo3')->nullable();
            $table->string('photo4')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vaivahik_panjiyans');
    }
};
