<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ekadash_sadasyata', function (Blueprint $table) {
            $table->string('photo')->nullable()->change();
            $table->string('signature')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ekadash_sadasyata', function (Blueprint $table) {
            $table->string('photo')->nullable(false)->change();
            $table->string('signature')->nullable(false)->change();
        });
    }
};
