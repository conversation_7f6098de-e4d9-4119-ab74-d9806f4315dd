

<?php $__env->startSection('title', 'दर्शनिक स्थल जानकारी'); ?>

<?php $__env->startSection('content'); ?>
    <div class="bg-gradient-to-b to-gray-50 min-h-screen">
        <div class="container-custom py-16">
            <!-- Page Header -->
            <div class="text-center mb-12">
                <div class="inline-block mx-auto mb-3">
                    <div class="w-10 h-1 bg-saffron-500 rounded-full mb-1 mx-auto"></div>
                    <div class="w-20 h-1 bg-saffron-500 rounded-full opacity-60 mx-auto"></div>
                </div>
                <h1 class="text-3xl md:text-4xl font-bold text-navy-900 mb-4">दर्शनिक स्थल जानकारी</h1>
                <p class="text-gray-600 max-w-2xl mx-auto">यादव समुदाय के लिए पर्यटन स्थलों और गाइड की जानकारी साझा करें</p>
            </div>

            <!-- Success Message -->
            <?php if(session('success')): ?>
                <div class="max-w-4xl mx-auto mb-8">
                    <div class="bg-green-50 border border-green-200 rounded-lg p-6">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-green-800">सफल!</h3>
                                <div class="mt-2 text-sm text-green-700">
                                    <p><?php echo e(session('success')); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Form -->
            <div class="max-w-4xl mx-auto">
                <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                    <div class="bg-gradient-to-r from-teal-600 to-teal-700 text-white p-6">
                        <h2 class="text-xl md:text-2xl font-bold mb-2">दर्शनिक स्थल पंजीकरण फॉर्म</h2>
                        <p class="text-teal-100">कृपया सभी आवश्यक जानकारी भरें</p>
                    </div>

                    <form action="<?php echo e(route('anya-seva.darshanik-ishthal.submit')); ?>" method="POST" class="p-6 md:p-8 space-y-6">
                        <?php echo csrf_field(); ?>

                        
                        <?php if($errors->any()): ?>
                            <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-red-800">कृपया निम्नलिखित त्रुटियों को ठीक करें:</h3>
                                        <div class="mt-2 text-sm text-red-700">
                                            <ul class="list-disc list-inside space-y-1">
                                                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <li><?php echo e($error); ?></li>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Tourist Place Information -->
                        <div class="bg-gray-50 rounded-lg p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">दर्शनिक स्थल की जानकारी</h3>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Place Name -->
                                <div class="md:col-span-2">
                                    <label for="place_name" class="block text-gray-700 font-semibold mb-2">
                                        दर्शनिक स्थल का नाम <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text" id="place_name" name="place_name" required value="<?php echo e(old('place_name')); ?>"
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200 <?php $__errorArgs = ['place_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                        placeholder="जैसे: खजुराहो, अजंता एलोरा, ताज महल आदि">
                                    <?php $__errorArgs = ['place_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <!-- Place Details -->
                                <div class="md:col-span-2">
                                    <label for="place_details" class="block text-gray-700 font-semibold mb-2">
                                        स्थान की विस्तृत जानकारी
                                    </label>
                                    <textarea id="place_details" name="place_details" rows="4" value="<?php echo e(old('place_details')); ?>"
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200"
                                        placeholder="स्थान का इतिहास, विशेषताएं, दर्शनीय स्थल, खुलने का समय आदि"><?php echo e(old('place_details')); ?></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Guide Information -->
                        <div class="bg-blue-50 rounded-lg p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">यादव टूर ट्रैवल्स गाइड की जानकारी</h3>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Guide Name -->
                                <div>
                                    <label for="guide_name" class="block text-gray-700 font-semibold mb-2">
                                        गाइड का नाम <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text" id="guide_name" name="guide_name" required value="<?php echo e(old('guide_name')); ?>"
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200 <?php $__errorArgs = ['guide_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                        placeholder="गाइड का पूरा नाम">
                                    <?php $__errorArgs = ['guide_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <!-- Guide Mobile -->
                                <div>
                                    <label for="guide_mobile" class="block text-gray-700 font-semibold mb-2">
                                        गाइड का मोबाइल नंबर <span class="text-red-500">*</span>
                                    </label>
                                    <input type="tel" id="guide_mobile" name="guide_mobile" required pattern="[0-9]{10}" value="<?php echo e(old('guide_mobile')); ?>"
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200 <?php $__errorArgs = ['guide_mobile'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                        placeholder="10 अंकों का मोबाइल नंबर">
                                    <?php $__errorArgs = ['guide_mobile'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <!-- Guide Address -->
                                <div class="md:col-span-2">
                                    <label for="guide_address" class="block text-gray-700 font-semibold mb-2">
                                        गाइड का पता
                                    </label>
                                    <textarea id="guide_address" name="guide_address" rows="3" value="<?php echo e(old('guide_address')); ?>"
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200"
                                        placeholder="गाइड का पूरा पता"><?php echo e(old('guide_address')); ?></textarea>
                                </div>

                                <!-- Guide Details -->
                                <div class="md:col-span-2">
                                    <label for="guide_details" class="block text-gray-700 font-semibold mb-2">
                                        गाइड की विशेषताएं और अनुभव
                                    </label>
                                    <textarea id="guide_details" name="guide_details" rows="3" value="<?php echo e(old('guide_details')); ?>"
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200"
                                        placeholder="गाइड का अनुभव, भाषाएं, विशेषज्ञता आदि"><?php echo e(old('guide_details')); ?></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Travel Information -->
                        <div class="bg-green-50 rounded-lg p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">यात्रा संबंधी जानकारी</h3>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Transport Info -->
                                <div>
                                    <label for="transport_info" class="block text-gray-700 font-semibold mb-2">
                                        वाहन संबंधित जानकारी
                                    </label>
                                    <textarea id="transport_info" name="transport_info" rows="4" value="<?php echo e(old('transport_info')); ?>"
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200"
                                        placeholder="बस, ट्रेन, हवाई जहाज, टैक्सी की सुविधा, किराया आदि"><?php echo e(old('transport_info')); ?></textarea>
                                </div>

                                <!-- Accommodation Info -->
                                <div>
                                    <label for="accommodation_info" class="block text-gray-700 font-semibold mb-2">
                                        रुकने संबंधित जानकारी
                                    </label>
                                    <textarea id="accommodation_info" name="accommodation_info" rows="4" value="<?php echo e(old('accommodation_info')); ?>"
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200"
                                        placeholder="होटल, धर्मशाला, गेस्ट हाउस, किराया, सुविधाएं आदि"><?php echo e(old('accommodation_info')); ?></textarea>
                                </div>

                                <!-- Other Info -->
                                <div class="md:col-span-2">
                                    <label for="other_info" class="block text-gray-700 font-semibold mb-2">
                                        अन्य महत्वपूर्ण जानकारी
                                    </label>
                                    <textarea id="other_info" name="other_info" rows="3" value="<?php echo e(old('other_info')); ?>"
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200"
                                        placeholder="खाना-पीना, स्थानीय बाजार, सावधानियां, बेस्ट टाइम टू विजिट आदि"><?php echo e(old('other_info')); ?></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Submitter Information -->
                        <div class="bg-yellow-50 rounded-lg p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">आपकी जानकारी</h3>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Submitter Name -->
                                <div>
                                    <label for="submitted_by_name" class="block text-gray-700 font-semibold mb-2">
                                        आपका नाम <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text" id="submitted_by_name" name="submitted_by_name" required value="<?php echo e(old('submitted_by_name')); ?>"
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200 <?php $__errorArgs = ['submitted_by_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                        placeholder="आपका पूरा नाम">
                                    <?php $__errorArgs = ['submitted_by_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <!-- Submitter Mobile -->
                                <div>
                                    <label for="submitted_by_mobile" class="block text-gray-700 font-semibold mb-2">
                                        आपका मोबाइल नंबर <span class="text-red-500">*</span>
                                    </label>
                                    <input type="tel" id="submitted_by_mobile" name="submitted_by_mobile" required pattern="[0-9]{10}" value="<?php echo e(old('submitted_by_mobile')); ?>"
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200 <?php $__errorArgs = ['submitted_by_mobile'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                        placeholder="10 अंकों का मोबाइल नंबर">
                                    <?php $__errorArgs = ['submitted_by_mobile'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <!-- Membership Number -->
                                <div class="md:col-span-2">
                                    <label for="membership_number" class="block text-gray-700 font-semibold mb-2">
                                        सदस्यता संख्या (वैकल्पिक)
                                    </label>
                                    <input type="text" id="membership_number" name="membership_number" value="<?php echo e(old('membership_number')); ?>"
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200"
                                        placeholder="यदि आप समिति के सदस्य हैं तो सदस्यता संख्या दर्ज करें">
                                    <p class="text-xs text-gray-500 mt-1">सदस्यता संख्या देने से आपकी जानकारी को प्राथमिकता मिलेगी</p>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="text-center pt-6">
                            <button type="submit"
                                class="bg-gradient-to-r from-teal-600 to-teal-700 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:from-teal-700 hover:to-teal-800 transition duration-200 shadow-lg transform hover:scale-105">
                                जानकारी जमा करें
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\web-yadav-samaj\resources\views/anya-seva/darshanik-ishthal.blade.php ENDPATH**/ ?>