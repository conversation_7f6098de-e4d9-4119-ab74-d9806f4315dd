@extends('layouts.app')

@section('title', 'नौकरी सहायता रिपोर्ट')

@section('content')
    <div class="bg-gradient-to-b to-gray-50 min-h-screen">
        <div class="container-custom py-16">
            <!-- Page Header -->
            <div class="text-center mb-12">
                <div class="inline-block mx-auto mb-3">
                    <div class="w-10 h-1 bg-saffron-500 rounded-full mb-1 mx-auto"></div>
                    <div class="w-20 h-1 bg-saffron-500 rounded-full opacity-60 mx-auto"></div>
                </div>
                <h1 class="text-3xl md:text-4xl font-bold text-navy-900 mb-4">नौकरी सहायता रिपोर्ट</h1>
                <p class="text-gray-600 max-w-2xl mx-auto">रोजगार और स्वरोजगार की तलाश में युवाओं की जानकारी</p>
            </div>

            <!-- Add New <PERSON> -->
            <div class="text-center mb-8">
                <a href="{{ route('anya-seva.naukri-sahayta') }}" 
                   class="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition duration-200 shadow-lg">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    नई जानकारी जोड़ें
                </a>
            </div>

            <!-- Success/Error Messages -->
            @if(session('success'))
                <div class="max-w-4xl mx-auto mb-8">
                    <div class="bg-green-50 border border-green-200 rounded-lg p-6">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-green-800">{{ session('success') }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            @if(session('error'))
                <div class="max-w-4xl mx-auto mb-8">
                    <div class="bg-red-50 border border-red-200 rounded-lg p-6">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-red-800">{{ session('error') }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Reports Grid -->
            <div class="max-w-7xl mx-auto">
                @if($reports->count() > 0)
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                        @foreach($reports as $report)
                            <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition duration-300">
                                <!-- Header -->
                                <div class="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-4">
                                    <h3 class="text-lg font-bold truncate">{{ $report->naam }}</h3>
                                    <p class="text-blue-100 text-sm">{{ $report->created_at->format('d/m/Y') }}</p>
                                </div>

                                <!-- Content -->
                                <div class="p-6">
                                    <!-- Basic Info -->
                                    <div class="mb-4">
                                        <div class="flex items-center mb-2">
                                            <svg class="w-4 h-4 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                            </svg>
                                            <span class="font-semibold text-gray-800">पिता का नाम:</span>
                                        </div>
                                        <p class="text-gray-700 ml-6">{{ $report->pita_ka_naam }}</p>
                                    </div>

                                    <!-- Age -->
                                    <div class="mb-4">
                                        <div class="flex items-center mb-2">
                                            <svg class="w-4 h-4 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                            </svg>
                                            <span class="font-semibold text-gray-800">उम्र:</span>
                                        </div>
                                        <p class="text-gray-700 ml-6">{{ $report->umra }} वर्ष</p>
                                    </div>

                                    <!-- Contact -->
                                    <div class="mb-4">
                                        <div class="flex items-center mb-2">
                                            <svg class="w-4 h-4 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                            </svg>
                                            <span class="font-semibold text-gray-800">संपर्क:</span>
                                        </div>
                                        <p class="text-gray-700 ml-6">{{ $report->mobile_number }}</p>
                                    </div>

                                    <!-- Qualifications Preview -->
                                    @if($report->ahartayen)
                                        <div class="mb-4">
                                            <div class="flex items-center mb-2">
                                                <svg class="w-4 h-4 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                                </svg>
                                                <span class="font-semibold text-gray-800">अहर्ताएं:</span>
                                            </div>
                                            <p class="text-gray-600 text-sm ml-6 line-clamp-2">{{ Str::limit($report->ahartayen, 80) }}</p>
                                        </div>
                                    @endif

                                    <!-- View Details Button -->
                                    <div class="mt-6">
                                        <button onclick="openVerificationModal({{ $report->id }})" 
                                                class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition duration-200 font-semibold">
                                            पूरी जानकारी देखें
                                        </button>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>

                    <!-- Pagination -->
                    <div class="flex justify-center">
                        {{ $reports->links() }}
                    </div>
                @else
                    <!-- No Reports Found -->
                    <div class="bg-white rounded-lg shadow-lg p-12 text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2h8z"></path>
                        </svg>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">कोई नौकरी सहायता आवेदन नहीं मिला</h3>
                        <p class="text-gray-500 mb-6">अभी तक कोई नौकरी सहायता आवेदन जमा नहीं किया गया है।</p>
                        <a href="{{ route('anya-seva.naukri-sahayta') }}" 
                           class="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition duration-200">
                            पहला आवेदन जोड़ें
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Verification Modal -->
    <div id="verificationModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">सदस्यता सत्यापन</h3>
                    <button type="button" onclick="closeVerificationModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <p class="text-sm text-gray-600 mb-4">पूरी जानकारी देखने के लिए कृपया अपनी सदस्यता संख्या दर्ज करें:</p>
                <form id="verificationForm" method="POST">
                    @csrf
                    <div class="mb-4">
                        <label for="membership_number" class="block text-sm font-medium text-gray-700 mb-2">सदस्यता संख्या:</label>
                        <input type="text" name="membership_number" id="membership_number" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" 
                               placeholder="जैसे: CGYSSS000001">
                    </div>
                    <div class="flex justify-end space-x-3">
                        <button type="button" onclick="closeVerificationModal()" 
                                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500">
                            रद्द करें
                        </button>
                        <button type="submit" 
                                class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            सत्यापित करें
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function openVerificationModal(reportId) {
            const modal = document.getElementById('verificationModal');
            const form = document.getElementById('verificationForm');
            form.action = `/naukri-sahayta-details/${reportId}`;
            modal.classList.remove('hidden');
        }

        function closeVerificationModal() {
            const modal = document.getElementById('verificationModal');
            modal.classList.add('hidden');
            document.getElementById('membership_number').value = '';
        }

        // Close modal when clicking outside
        document.getElementById('verificationModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeVerificationModal();
            }
        });
    </script>
@endsection
