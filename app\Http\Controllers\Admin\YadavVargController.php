<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\YadavVarg;
use Illuminate\Http\Request;

class YadavVargController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = YadavVarg::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('name_english', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('category', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        $yadavVargs = $query->ordered()->paginate(15);

        return view('admin.yadav-vargs.index', compact('yadavVargs'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.yadav-vargs.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:yadav_vargs,name',
                'name_english' => 'nullable|string|max:255',
                'description' => 'nullable|string',
                'category' => 'nullable|string|max:255',
                'is_active' => 'boolean',
                'display_order' => 'nullable|integer|min:0',
            ]);

            $validated['is_active'] = $request->has('is_active');
            $validated['display_order'] = $validated['display_order'] ?? 0;

            YadavVarg::create($validated);

            return redirect()->route('admin.yadav-vargs.index')
                           ->with('success', 'यादव वर्ग सफलतापूर्वक जोड़ा गया।');

        } catch (\Exception $e) {
            return redirect()->back()
                           ->withInput()
                           ->withErrors(['error' => 'यादव वर्ग जोड़ने में त्रुटि: ' . $e->getMessage()]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(YadavVarg $yadavVarg)
    {
        return view('admin.yadav-vargs.show', compact('yadavVarg'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(YadavVarg $yadavVarg)
    {
        return view('admin.yadav-vargs.edit', compact('yadavVarg'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, YadavVarg $yadavVarg)
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:yadav_vargs,name,' . $yadavVarg->id,
                'name_english' => 'nullable|string|max:255',
                'description' => 'nullable|string',
                'category' => 'nullable|string|max:255',
                'is_active' => 'boolean',
                'display_order' => 'nullable|integer|min:0',
            ]);

            $validated['is_active'] = $request->has('is_active');
            $validated['display_order'] = $validated['display_order'] ?? 0;

            $yadavVarg->update($validated);

            return redirect()->route('admin.yadav-vargs.index')
                           ->with('success', 'यादव वर्ग सफलतापूर्वक अपडेट किया गया।');

        } catch (\Exception $e) {
            return redirect()->back()
                           ->withInput()
                           ->withErrors(['error' => 'यादव वर्ग अपडेट करने में त्रुटि: ' . $e->getMessage()]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(YadavVarg $yadavVarg)
    {
        try {
            // Check if this yadav varg is being used by any memberships or vaivahik panjiyans
            $membershipCount = $yadavVarg->memberships()->count();
            $vaivahikCount = $yadavVarg->vaivahikPanjiyans()->count();

            if ($membershipCount > 0 || $vaivahikCount > 0) {
                return redirect()->back()
                               ->withErrors(['error' => 'इस यादव वर्ग को हटाया नहीं जा सकता क्योंकि यह उपयोग में है।']);
            }

            $yadavVarg->delete();

            return redirect()->route('admin.yadav-vargs.index')
                           ->with('success', 'यादव वर्ग सफलतापूर्वक हटाया गया।');

        } catch (\Exception $e) {
            return redirect()->back()
                           ->withErrors(['error' => 'यादव वर्ग हटाने में त्रुटि: ' . $e->getMessage()]);
        }
    }
}
