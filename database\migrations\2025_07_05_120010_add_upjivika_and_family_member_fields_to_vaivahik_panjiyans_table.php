<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('vaivahik_panjiyans', function (Blueprint $table) {
            // Add upjivika field after vartaman_karya
            $table->string('upjivika')->nullable()->after('vartaman_karya');
            
            // Add family member membership number field (required)
            $table->string('family_member_membership_number')->after('upjivika');
            
            // Add index for faster membership number lookups
            $table->index('family_member_membership_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('vaivahik_panjiyans', function (Blueprint $table) {
            // Drop the index first
            $table->dropIndex(['family_member_membership_number']);
            
            // Drop the columns
            $table->dropColumn(['upjivika', 'family_member_membership_number']);
        });
    }
};
