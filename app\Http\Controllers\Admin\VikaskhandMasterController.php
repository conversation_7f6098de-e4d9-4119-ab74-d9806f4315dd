<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\VikaskhandMaster;
use App\Models\DistrictMaster;
use Illuminate\Http\Request;

class VikaskhandMasterController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = VikaskhandMaster::with('district.division');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('sub_district_name_eng', 'like', "%{$search}%")
                  ->orWhere('sub_district_name_hin', 'like', "%{$search}%")
                  ->orWhere('sub_district_lgd_code', 'like', "%{$search}%");
            });
        }

        // Status filter
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->active();
            } elseif ($request->status === 'inactive') {
                $query->inactive();
            }
        }

        // District filter
        if ($request->filled('district_lgd_code')) {
            $query->where('district_lgd_code', $request->district_lgd_code);
        }

        $vikaskhands = $query->ordered()->paginate(15);
        $districts = DistrictMaster::active()->ordered()->get();

        return view('admin.vikaskhand-masters.index', compact('vikaskhands', 'districts'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $districts = DistrictMaster::active()->ordered()->get();
        return view('admin.vikaskhand-masters.create', compact('districts'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'sub_district_lgd_code' => 'required|string|max:255|unique:vikaskhand_masters,sub_district_lgd_code',
                'sub_district_name_eng' => 'required|string|max:255',
                'sub_district_name_hin' => 'required|string|max:255',
                'district_lgd_code' => 'required|string|exists:district_masters,district_lgd_code',
                'is_active' => 'boolean',
                'display_order' => 'nullable|integer|min:0',
            ]);

            $validated['is_active'] = $request->has('is_active');
            $validated['display_order'] = $validated['display_order'] ?? 0;

            VikaskhandMaster::create($validated);

            return redirect()->route('admin.vikaskhand-masters.index')
                           ->with('success', 'विकासखंड सफलतापूर्वक जोड़ा गया।');
        } catch (\Exception $e) {
            return redirect()->back()
                           ->withInput()
                           ->with('error', 'विकासखंड जोड़ने में त्रुटि: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(VikaskhandMaster $vikaskhandMaster)
    {
        $vikaskhandMaster->load('district.division');
        return view('admin.vikaskhand-masters.show', compact('vikaskhandMaster'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(VikaskhandMaster $vikaskhandMaster)
    {
        $districts = DistrictMaster::active()->ordered()->get();
        return view('admin.vikaskhand-masters.edit', compact('vikaskhandMaster', 'districts'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, VikaskhandMaster $vikaskhandMaster)
    {
        try {
            $validated = $request->validate([
                'sub_district_lgd_code' => 'required|string|max:255|unique:vikaskhand_masters,sub_district_lgd_code,' . $vikaskhandMaster->id,
                'sub_district_name_eng' => 'required|string|max:255',
                'sub_district_name_hin' => 'required|string|max:255',
                'district_lgd_code' => 'required|string|exists:district_masters,district_lgd_code',
                'is_active' => 'boolean',
                'display_order' => 'nullable|integer|min:0',
            ]);

            $validated['is_active'] = $request->has('is_active');
            $validated['display_order'] = $validated['display_order'] ?? 0;

            $vikaskhandMaster->update($validated);

            return redirect()->route('admin.vikaskhand-masters.index')
                           ->with('success', 'विकासखंड सफलतापूर्वक अपडेट किया गया।');
        } catch (\Exception $e) {
            return redirect()->back()
                           ->withInput()
                           ->with('error', 'विकासखंड अपडेट करने में त्रुटि: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(VikaskhandMaster $vikaskhandMaster)
    {
        try {
            $vikaskhandMaster->delete();
            return redirect()->route('admin.vikaskhand-masters.index')
                           ->with('success', 'विकासखंड सफलतापूर्वक हटाया गया।');
        } catch (\Exception $e) {
            return redirect()->back()
                           ->with('error', 'विकासखंड हटाने में त्रुटि: ' . $e->getMessage());
        }
    }
}
