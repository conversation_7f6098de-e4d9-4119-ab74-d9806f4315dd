<?php

namespace App\Http\Controllers;

use App\Models\PramukhPadadhikari;
use App\Models\DivisionMaster;
use App\Models\DistrictMaster;
use App\Models\VikaskhandMaster;
use Illuminate\Http\Request;

class PramukhPadadhikariController extends Controller
{
    /**
     * Show Pramukh <PERSON>karis grouped by <PERSON><PERSON><PERSON> (Division).
     * Only shows records where division is selected but district and vikaskhand are NULL.
     */
    public function sambhagWise(Request $request)
    {
        $query = PramukhPadadhikari::with(['division', 'district', 'vikaskhand'])
                                  ->where('active', true)
                                  ->whereNotNull('division_code')
                                  ->whereNull('district_lgd_code')
                                  ->whereNull('vikaskhand_lgd_code');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('naam', 'like', "%{$search}%")
                  ->orWhere('sangathan_me_padnaam', 'like', "%{$search}%")
                  ->orWhere('mobile_number', 'like', "%{$search}%")
                  ->orWhereHas('division', function ($divQuery) use ($search) {
                      $divQuery->where('division_name_hin', 'like', "%{$search}%")
                               ->orWhere('division_name_eng', 'like', "%{$search}%");
                  });
            });
        }

        // Division filter
        if ($request->filled('division_code')) {
            $query->where('division_code', $request->division_code);
        }

        $pramukhPadadhikaris = $query->orderBy('division_code')
                                    ->orderBy('naam')
                                    ->paginate(15);

        $divisions = DivisionMaster::active()->ordered()->get();

        return view('pramukh-padadhikari.sambhag-wise', compact('pramukhPadadhikaris', 'divisions'));
    }

    /**
     * Show Pramukh Padadhikaris grouped by Jila (District).
     * Only shows records where district is selected but vikaskhand is NULL.
     */
    public function jilaWise(Request $request)
    {
        $query = PramukhPadadhikari::with(['division', 'district', 'vikaskhand'])
                                  ->where('active', true)
                                  ->whereNotNull('district_lgd_code')
                                  ->whereNull('vikaskhand_lgd_code');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('naam', 'like', "%{$search}%")
                  ->orWhere('sangathan_me_padnaam', 'like', "%{$search}%")
                  ->orWhere('mobile_number', 'like', "%{$search}%")
                  ->orWhereHas('district', function ($distQuery) use ($search) {
                      $distQuery->where('district_name_hin', 'like', "%{$search}%")
                               ->orWhere('district_name_eng', 'like', "%{$search}%");
                  });
            });
        }

        // Division filter
        if ($request->filled('division_code')) {
            $query->where('division_code', $request->division_code);
        }

        // District filter
        if ($request->filled('district_lgd_code')) {
            $query->where('district_lgd_code', $request->district_lgd_code);
        }

        $pramukhPadadhikaris = $query->orderBy('district_lgd_code')
                                    ->orderBy('naam')
                                    ->paginate(15);

        $divisions = DivisionMaster::active()->ordered()->get();
        $districts = DistrictMaster::active()->ordered()->get();

        return view('pramukh-padadhikari.jila-wise', compact('pramukhPadadhikaris', 'divisions', 'districts'));
    }

    /**
     * Show Pramukh Padadhikaris grouped by Vikaskhand.
     */
    public function vikaskhandWise(Request $request)
    {
        $query = PramukhPadadhikari::with(['division', 'district', 'vikaskhand'])
                                  ->where('active', true)
                                  ->whereNotNull('vikaskhand_lgd_code');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('naam', 'like', "%{$search}%")
                  ->orWhere('sangathan_me_padnaam', 'like', "%{$search}%")
                  ->orWhere('mobile_number', 'like', "%{$search}%")
                  ->orWhereHas('vikaskhand', function ($vikQuery) use ($search) {
                      $vikQuery->where('sub_district_name_hin', 'like', "%{$search}%")
                               ->orWhere('sub_district_name_eng', 'like', "%{$search}%");
                  });
            });
        }

        // Division filter
        if ($request->filled('division_code')) {
            $query->where('division_code', $request->division_code);
        }

        // District filter
        if ($request->filled('district_lgd_code')) {
            $query->where('district_lgd_code', $request->district_lgd_code);
        }

        // Vikaskhand filter
        if ($request->filled('vikaskhand_lgd_code')) {
            $query->where('vikaskhand_lgd_code', $request->vikaskhand_lgd_code);
        }

        $pramukhPadadhikaris = $query->orderBy('vikaskhand_lgd_code')
                                    ->orderBy('naam')
                                    ->paginate(15);

        $divisions = DivisionMaster::active()->ordered()->get();
        $districts = DistrictMaster::active()->ordered()->get();
        $vikaskhands = VikaskhandMaster::active()->ordered()->get();

        return view('pramukh-padadhikari.vikaskhand-wise', compact('pramukhPadadhikaris', 'divisions', 'districts', 'vikaskhands'));
    }
}
